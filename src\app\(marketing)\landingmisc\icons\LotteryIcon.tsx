import * as React from "react";
import { SVGProps } from "react";

const LotteryIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={3}
      y={4}
      width={18}
      height={16}
      rx={2}
      stroke="currentColor"
      strokeWidth={2}
      fill="none"
    />
    <circle cx={8} cy={10} r={1.5} fill="currentColor" />
    <circle cx={12} cy={10} r={1.5} fill="currentColor" />
    <circle cx={16} cy={10} r={1.5} fill="currentColor" />
    <circle cx={8} cy={14} r={1.5} fill="currentColor" />
    <circle cx={12} cy={14} r={1.5} fill="currentColor" />
    <circle cx={16} cy={14} r={1.5} fill="currentColor" />
    <path
      d="M7 4V2M17 4V2"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
    />
  </svg>
);

export default LotteryIcon;
