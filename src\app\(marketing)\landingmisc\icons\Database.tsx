import * as React from "react";
import { SVGProps } from "react";
const Database = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={60}
    height={60}
    viewBox="0 0 60 60"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={12.572}
      width={48.572}
      height={48.572}
      rx={10}
      transform="rotate(15 12.572 0)"
      fill="#EAD8F9"
    />
    <g
      clipPath="url(#a)"
      stroke="#4E2667"
      strokeWidth={1.667}
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M30.863 26.78c4 1.072 7.534.86 7.891-.473.358-1.334-2.596-3.284-6.597-4.356s-7.534-.86-7.892.473c-.357 1.334 2.597 3.284 6.598 4.356m6.381 5.161c-.358 1.336-3.867 1.552-7.892.474-4.024-1.079-6.955-3.02-6.597-4.356" />
      <path d="m24.265 22.424-3.02 11.27c-.358 1.336 2.573 3.277 6.598 4.355 4.024 1.079 7.533.863 7.891-.473l3.02-11.27" />
    </g>
    <defs>
      <clipPath id="a">
        <path
          fill="#fff"
          d="m22.929 17.753 19.319 5.176-5.177 19.319-19.318-5.177z"
        />
      </clipPath>
    </defs>
  </svg>
);
export default Database;
