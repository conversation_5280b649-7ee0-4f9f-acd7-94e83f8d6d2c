import * as React from "react";
import { SVGProps } from "react";
const Flag = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={49}
    height={49}
    viewBox="0 0 49 49"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={10.459}
      y={0.184}
      width={39.7}
      height={39.7}
      rx={9.85}
      transform="rotate(15 10.459 .184)"
      fill="#EAD8F9"
      stroke="#4E2667"
      strokeWidth={0.3}
    />
    <g clipPath="url(#a)">
      <path
        d="M31.092 28.85a1.666 1.666 0 0 1-2.04 1.18l-9.66-2.589-4.083 2.357L18.76 16.92a1.666 1.666 0 0 1 2.041-1.178l11.27 3.02a1.667 1.667 0 0 1 1.178 2.04z"
        stroke="#4E2667"
        strokeWidth={1.667}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path
          fill="#fff"
          d="m17.424 12.247 19.318 5.177-5.176 19.318-19.319-5.176z"
        />
      </clipPath>
    </defs>
  </svg>
);
export default Flag;
