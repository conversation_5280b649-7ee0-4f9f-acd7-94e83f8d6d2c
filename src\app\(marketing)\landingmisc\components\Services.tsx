'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import Arrow from '../icons/Arrow';
import { motion, useAnimation } from 'framer-motion';
import Link from 'next/link';

export default function Services() {

    const Services = [
        {
            id: '1',
            image: '/images/CompanyLogoOne.png',
            companyName: 'Bulk Messaging',
            companyDescription:
                'Whether you are launching a product, confirming a loan, or running a nationwide promo, our bulk messaging platform delivers fast, reliable communication to your audience via SMS and voice...',
            link: 'Learn more',
            icon: <Arrow />,
            href: '/services/bulkmessaging'
        },

        {
            id: '2',
            image: '/images/CompanyLogoOne.png',
            companyName: 'Lottery Services',
            companyDescription:
                'Our mobile-first lottery solutions offer compliant, seamless, and rewarding lottery mechanics for businesses ready to tap into the excitement of chance-based engagement...',
            link: 'Learn more',
            icon: <Arrow />,
            href: '/services/lottery'
        },

        {
            id: '3',
            image: '/images/CompanyLogoOne.png',
            companyName: 'Short Code & USSD Services',
            companyDescription:
                'We deliver robust short code and USSD services that connect your brand directly to millions of mobile users—anytime, anywhere...',
            link: 'Learn more',
            icon: <Arrow />,
            href: '/services/shortcode'
        },

        {
            id: '4',
            image: '/images/CompanyLogoOne.png',
            companyName: 'Gamification',
            companyDescription:
                'At Whispa Konnect, we build powerful gamification engines that transform ordinary consumer interactions into unforgettable experiences...',
            link: 'Learn more',
            icon: <Arrow />,
            href: '/services/gamification/'
        },
    ];

    const controls = useAnimation();

    useEffect(() => {
        controls.start({
            x: ['0%', '-50%'],
            transition: {
                x: {
                    repeat: Infinity,
                    repeatType: 'loop',
                    duration: 20,
                    ease: 'linear',
                },
            },
        });
    }, [controls]);

    return (
        <div className="bg-[#EEE0FA] sm:py-[4.5rem] py-[2rem] overflow-hidden">
            <div className="flex items-center justify-center">
                <p className="font-semibold sm:text-[1.125rem] text-sm text-[#4E2667] bg-[#EBCCFF] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
                    Our Services
                </p>
            </div>
            <div className="flex items-center justify-center mx-auto max-w-[54rem] mt-2 text-center px-4">
                <span className="text-[#4E2667E5]/90 font-bold sm:text-[1.875rem] text-[1rem]">
                    Whispa Konnect offers a range of services to enhance your business communication and engagement.
                </span>
            </div>

            <div className="mt-[3.5rem] w-full overflow-hidden">
                <motion.div
                    className="flex gap-[2rem] w-max"
                    animate={controls}
                >
                    {[...Services, ...Services].map((service, index) => (
                        <div
                            key={`${service.id}-${index}`}
                            className="rounded-[1.25rem] p-2 bg-gradient-to-bl from-[#FB37FF] via-[#9B6FEE0A] to-[#18B2DF] flex-shrink-0 w-[300px] "
                        >
                            <div className="bg-[#EEE0FA] rounded-[0.75rem] h-full py-[2rem] px-[1.5rem] flex flex-col justify-between">
                                <Image
                                    src={service.image}
                                    alt={service.companyName}
                                    width={60}
                                    height={60}
                                />
                                <p className="mt-[1rem] font-bold text-[1rem] text-[#4E2667] sm:text-[1.375rem]">
                                    {service.companyName}
                                </p>
                                <p className="text-[#4E2667] leading-[1.375rem] text-sm sm:text-[1rem] mt-[0.75rem]">
                                    {service.companyDescription}
                                </p>
                                <Link
                                    href={service.href}
                                    className="flex items-center text-[#4E2667] font-normal cursor-pointer gap-2 mt-[1rem]"
                                >
                                    <span className="text-sm">{service.link}</span>
                                    <span>{service.icon}</span>
                                </Link>

                            </div>
                        </div>
                    ))}
                </motion.div>
            </div>
        </div>
    );
}
