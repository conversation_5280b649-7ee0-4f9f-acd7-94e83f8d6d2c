import React from 'react'
import Image from 'next/image'
import Balancer from 'react-wrap-balancer'
export default function Vision() {
    return (

        <div className="">
            <div className=" 2xl:px-[6.25rem] px-[1.5rem] mt-[4.5rem] flex flex-col md:flex-row items-center justify-between  gap-[4.5rem] py-[4.5rem]">


                <div className="w-full  md:w-1/2  lg:max-w-[580px]  rounded-[20px]  mx-auto h-full">
                    <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden h-full ">
                        <Image
                            alt="Vision"
                            src="/images/Vision.png"
                            fill
                            className="object-cover"
                        />
                    </div>
                </div>



                <div className="w-full ">


                    <div>
                        <Balancer >
                            <span className='font-bold text-[2.625rem] font-sans text-[#4E2667E5]/90'>Our Vision</span>
                        </Balancer>

                        <div className="mt-6  text-[#4E2667E5]/80">
                            <Balancer>
                                <p className="sm:text-[1.5rem] text-[1.19rem] font-sans font-normal ">
                                    To become Africa’s most trusted and innovative provider of gamified marketing and mobile communication solutions, transforming how businesses interact with people.

                                </p>
                            </Balancer>

                            <Balancer>
                                <p className="sm:text-[1.5rem] text-[1.19rem]  mt-[1rem] font-normal  ">
                                    Our vision is rooted in the belief that technology, when used creatively and ethically, can bridge gaps, create meaningful engagement, and transform industries.
                                    As a proudly African company with licenses from the Nigerian Communications Commission (NCC) and Lagos State Lottery and Gaming Authority, we envision a continent where every business from small shops to large enterprises has the tools to thrive in a digital-first world.
                                </p>

                            </Balancer>

                            <Balancer>
                                <p className="sm:text-[1.5rem] text-[1.19rem]  mt-[1rem] font-normal  ">
                                    We are building an ecosystem where communication isn’t just possible, it is strategic, interactive, and measurable.
                                </p>
                            </Balancer>


                        </div>
                    </div>
                </div>




            </div>
        </div>

    )
}
