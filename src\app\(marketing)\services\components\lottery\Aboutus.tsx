
import React from 'react'
import Image from 'next/image'
import Balancer from 'react-wrap-balancer'

import { Button } from '@/components/core/Button';


export default function Aboutus() {
    return (
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10 lg:gap-[4.5rem] px-4 sm:px-8 lg:px-20 py-16 bg-gradient-to-r from-[#9348B8] to-[#460666]">

            <div className="w-full [@media(min-width:1200px)]:flex-1">
                <p className="text-[#F9F9F9] text-sm sm:text-base font-semibold mb-2">About Our</p>

                <div>
                    <Balancer>
                        <span className='font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]'>Lottery </span>
                        <span className="block font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]/90">Solutions</span>
                    </Balancer>

                    <div className="mt-6  text-[#F9F9F9CC]/80">
                        <Balancer>
                            <p className="text-[1rem] sm:text-[1.25rem] ">
                                Our mobile-first lottery solutions offer compliant, seamless, and rewarding lottery mechanics for businesses ready to tap into the excitement of game of chance engagement. We provide everything you need from infrastructure and draw mechanics to payment integration and winner verification.
                                Whether it&apos;s airtime lotteries, instant win campaigns, loyalty raffles, or nationwide cash prize draws, Whispa Konnect powers highly secure and fully trackable lottery systems via USSD, SMS, and web. It&apos;s the perfect solution for VAS providers, telcos, and brands seeking to drive mass participation.


                            </p>
                        </Balancer>

                        <a
                            href='https://wisewinn.com'
                            target="_blank"
                            rel="noopener noreferrer"
                        >

                            <Button className="mt-[42px] bg-[#F9F9F9] sm:px-[4.5rem] text-[#4C1961] md:px-[2.8rem] lg:px-[4.5rem] py-[1rem] whitespace-nowrap text-[1rem] font-medium rounded-[0.5rem] w-full sm:w-auto shadow-md cursor-pointer">
                                Play Now
                            </Button>
                        </a>


                    </div>
                </div>
            </div>


            {/* <div className="aspect-[586.29/587] px-[0.6787rem] py-[0.8rem] bg-[#E7EDF0] rounded-[20px] flex items-center justify-center ">
                <Image
                    alt="AboutImage"
                    src="/images/AboutWoman.png"
                    width={586.29}
                    height={587}
                    className="rounded-[20px] w-full "
                />
            </div> */}

            <div className="w-full max-w-[580px] bg-[#E7EDF0] rounded-[20px] p-3 mx-auto ">
                <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden ">
                    <Image
                        alt="AboutImage"
                        src="/images/LotteryAbout.png"
                        fill
                        className="object-cover"
                    />
                </div>
            </div>



        </div>
    );
}
