'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import { cn } from '@/utils/classNames';
import { NavigationMenu, NavigationMenuContent, NavigationMenuItem, NavigationMenuLink, NavigationMenuList, NavigationMenuTrigger } from '@/components/core/DropdownNavigation';
import { ProductsDropdown } from './ProductsDropdown';


interface DesktopMenuLinkProps {
  link: string;
  text: string;
  disabled: boolean;
  isExternal: boolean;
}

function DesktopMenuLink({
  text,
  link,
  disabled,
  isExternal,
}: DesktopMenuLinkProps) {
  const pathname = usePathname();
  const isSelected = pathname === link;

  const getTextStyles = () => {
    const purpleLinks = ['Home', 'Services', 'About us'];
    const baseFontWeight = isSelected ? 'font-bold' : 'font-normal';

    if (text === 'Contact us') {
      if (pathname === '/' || pathname.startsWith('/services')) {
        return cn('text-[#F9F9F9]', baseFontWeight);
      } else {
        return cn('text-[#4E2667]', baseFontWeight, 'text-[1rem] font-sans');
      }
    }

    if (text === 'About us') {
      return cn(
        'text-[#4E2667]',          // default for mobile
        'md:text-white lg:text-[#4E2667]',           // white from md screens and up
        baseFontWeight,
        'text-[1rem] font-sans'
      );
    }

    if (purpleLinks.includes(text)) {
      return cn('text-[#4E2667]', baseFontWeight, 'text-[1rem] font-sans');
    }

    return cn('text-black', baseFontWeight);
  };

  if (isExternal) {
    return (
      <a
        className={cn(
          'inline-block px-3 py-2.5 text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 xl:px-6 xl:py-[1.375rem] xl:text-base',
          getTextStyles()
        )}
        href={link}
        rel="noopener noreferrer"
        target="_blank"
      >
        {text}
      </a>
    );
  }

  if (disabled) {
    return (
      <button
        className={cn(
          'inline-block cursor-not-allowed bg-[#fafafa] px-3 py-2.5 text-sm opacity-50 xl:px-6 xl:py-[1.375rem] xl:text-base',
          getTextStyles()
        )}
        disabled
      >
        {text}
      </button>
    );
  }

  return (
    <Link
      className={cn(
        'inline-block px-3 py-2.5 text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 xl:px-6 xl:py-[1.375rem] xl:text-base',
        getTextStyles()
      )}
      href={link}
    >
      {text}
    </Link>
  );
}

const linkGroups = [
  { link: '/', text: 'Home', icon: undefined, disabled: false, isExternal: false },
  { link: '/products', text: 'Products', icon: undefined, disabled: false, isExternal: false },
  { link: '/services', text: 'Services', icon: undefined, disabled: false, isExternal: false },
  { link: '/marketingsections/about-us', text: 'About us', icon: undefined, disabled: false, isExternal: false },
  { link: '/marketingsections/contact-us', text: 'Contact us', icon: undefined, disabled: false, isExternal: false },
];

const servicesDropdownItems = [
  {
    text: 'Bulk Messaging',
    href: '/services/bulkmessaging',
  },

  {
    text: 'Lottery',
    href: '/services/lottery'
  },

  {
    text: 'Shortcode & USSD',
    href: '/services/shortcode'
  }

];

interface DesktopMenuBarProps {
  isColored: boolean;
}

export function DesktopMenuBar({ isColored }: DesktopMenuBarProps) {
  return (
    <nav className="hidden md:block">
      <NavigationMenu>
        <NavigationMenuList
          className={cn(
            'flex items-center gap-x-px rounded-[3.75rem] px-3 transition-all duration-300 ease-in-out xl:px-6',
            isColored && ''
          )}
        >
          {linkGroups.map(({ link, text, disabled, isExternal }) => {
            if (text === 'Products') {
              return (
                <NavigationMenuItem key={link}>
                  <NavigationMenuTrigger className="text-[#4E2667] bg-transparent shadow-none hover:bg-transparent px-3 xl:px-6">
                    {text}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent className="bg-transparent border-none shadow-none p-0">
                    <ProductsDropdown />
                  </NavigationMenuContent>
                </NavigationMenuItem>
              );
            }

            if (text === 'Services') {
              return (
                <NavigationMenuItem key={link}>
                  <NavigationMenuTrigger className="text-[#4E2667] bg-transparent shadow-none hover:bg-transparent px-3 xl:px-6">
                    {text}
                  </NavigationMenuTrigger>
                  <NavigationMenuContent className="bg-white rounded-[0.5rem] shadow-lg px-[1rem] min-w-[10rem]">
                    <ul className="flex flex-col gap-1">
                      {servicesDropdownItems.map((item) => (
                        <li key={item.href}>
                          <Link
                            href={item.href}
                            className="block cursor-pointer  rounded-md text-sm text-[#000] hover:text-[#4E2667] transition-colors"
                          >
                            {item.text}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  </NavigationMenuContent>

                </NavigationMenuItem>
              );
            }

            return (
              <NavigationMenuItem key={link}>
                <NavigationMenuLink asChild>
                  <DesktopMenuLink
                    disabled={disabled}
                    isExternal={isExternal}
                    link={link}
                    text={text}
                  />
                </NavigationMenuLink>
              </NavigationMenuItem>
            );
          })}
        </NavigationMenuList>
      </NavigationMenu>
    </nav>
  );
}
