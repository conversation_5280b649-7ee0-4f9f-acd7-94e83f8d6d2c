"use client"

import Link from "next/link"
import { useState } from "react"
import { usePathname } from "next/navigation"
import { cn } from "@/utils/classNames"
import { ProductsDropdown } from "./ProductsDropdown"

interface DesktopMenuLinkProps {
  link: string
  text: string
  disabled: boolean
  isExternal: boolean
}

function DesktopMenuLink({ text, link, disabled, isExternal }: DesktopMenuLinkProps) {
  const pathname = usePathname()
  const isSelected = pathname === link

  const getTextStyles = () => {
    const purpleLinks = ["Home", "Services", "About us"]
    const baseFontWeight = isSelected ? "font-bold" : "font-normal"

    if (text === "Contact us") {
      if (pathname === "/" || pathname.startsWith("/services")) {
        return cn("text-[#F9F9F9]", baseFontWeight)
      } else {
        return cn("text-[#4E2667]", baseFontWeight, "text-[1rem] font-sans")
      }
    }

    if (text === "About us") {
      return cn("text-[#4E2667]", "md:text-white lg:text-[#4E2667]", baseFontWeight, "text-[1rem] font-sans")
    }

    if (purpleLinks.includes(text)) {
      return cn("text-[#4E2667]", baseFontWeight, "text-[1rem] font-sans")
    }

    return cn("text-black", baseFontWeight)
  }

  if (isExternal) {
    return (
      <a
        className={cn(
          "inline-block px-3 py-2.5 text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 xl:px-6 xl:py-[1.375rem] xl:text-base",
          getTextStyles(),
        )}
        href={link}
        rel="noopener noreferrer"
        target="_blank"
      >
        {text}
      </a>
    )
  }

  if (disabled) {
    return (
      <button
        className={cn(
          "inline-block cursor-not-allowed bg-[#fafafa] px-3 py-2.5 text-sm opacity-50 xl:px-6 xl:py-[1.375rem] xl:text-base",
          getTextStyles(),
        )}
        disabled
      >
        {text}
      </button>
    )
  }

  return (
    <Link
      className={cn(
        "inline-block px-3 py-2.5 text-sm transition-all duration-300 ease-in-out hover:-translate-y-0.5 xl:px-6 xl:py-[1.375rem] xl:text-base",
        getTextStyles(),
      )}
      href={link}
    >
      {text}
    </Link>
  )
}

const linkGroups = [
  { link: "/", text: "Home", icon: undefined, disabled: false, isExternal: false },
  { link: "/products", text: "Products", icon: undefined, disabled: false, isExternal: false },
  { link: "/services", text: "Services", icon: undefined, disabled: false, isExternal: false },
  { link: "/marketingsections/about-us", text: "About us", icon: undefined, disabled: false, isExternal: false },
  { link: "/marketingsections/contact-us", text: "Contact us", icon: undefined, disabled: false, isExternal: false },
]

const servicesDropdownItems = [
  {
    text: "Bulk Messaging",
    href: "/services/bulkmessaging",
    description: "Send messages to thousands of users instantly",
  },
  {
    text: "Lottery",
    href: "/services/lottery",
    description: "Complete lottery management solutions",
  },
  {
    text: "Shortcode & USSD",
    href: "/services/shortcode",
    description: "Interactive SMS and USSD services",
  },
]

interface DesktopMenuBarProps {
  isColored: boolean
}

export function DesktopMenuBar({ isColored }: DesktopMenuBarProps) {
  const [activeDropdown, setActiveDropdown] = useState<string | null>(null)

  const handleMouseEnter = (dropdown: string) => {
    setActiveDropdown(dropdown)
  }

  const handleMouseLeave = () => {
    setTimeout(() => {
      setActiveDropdown(null)
    }, 300)
  }

  return (
    <nav className="hidden md:block relative z-50">
      <ul
        className={cn(
          "flex items-center gap-x-px rounded-[3.75rem] px-3 transition-all duration-300 ease-in-out xl:px-6 xl:py-[1.375rem]",
          isColored && "",
        )}
      >
        {linkGroups.map(({ link, text, disabled, isExternal }) => {
          if (text === "Products") {
            return (
              <li
                key={link}
                className="relative z-50"
                onMouseEnter={() => handleMouseEnter("products")}
                onMouseLeave={handleMouseLeave}
              >
                <button className="text-[#4E2667] bg-transparent shadow-none hover:bg-gray-50 px-3 xl:px-6 py-2.5 xl:py-[1.375rem] transition-colors duration-200 flex items-center gap-1">
                  {text}
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {activeDropdown === "products" && (
                  <div
                    className="absolute top-full left-1/2 transform -translate-x-1/2 pt-1 z-[9999]"
                    onMouseEnter={() => handleMouseEnter("products")}
                    onMouseLeave={handleMouseLeave}
                  >
                    <ProductsDropdown />
                  </div>
                )}
              </li>
            )
          }

          if (text === "Services") {
            return (
              <li
                key={link}
                className="relative z-50"
                onMouseEnter={() => handleMouseEnter("services")}
                onMouseLeave={handleMouseLeave}
              >
                <button className="text-[#4E2667] bg-transparent shadow-none hover:bg-gray-50 px-3 xl:px-6 py-2.5 xl:py-[1.375rem] transition-colors duration-200 flex items-center gap-1">
                  {text}
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </button>

                {activeDropdown === "services" && (
                  <div
                    className="absolute top-full left-1/2 transform -translate-x-1/2 pt-1 z-[9999]"
                    onMouseEnter={() => handleMouseEnter("services")}
                    onMouseLeave={handleMouseLeave}
                  >
                    <div className="bg-white rounded-lg shadow-xl border border-gray-100 py-2 min-w-[280px]">
                      {servicesDropdownItems.map((item) => (
                        <Link
                          key={item.href}
                          href={item.href}
                          className="block px-4 py-3 hover:bg-gray-50 transition-colors duration-200 group"
                        >
                          <div className="font-medium text-gray-900 group-hover:text-[#4E2667] transition-colors">
                            {item.text}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">{item.description}</div>
                        </Link>
                      ))}
                    </div>
                  </div>
                )}
              </li>
            )
          }

          return (
            <li key={link}>
              <DesktopMenuLink disabled={disabled} isExternal={isExternal} link={link} text={text} />
            </li>
          )
        })}
      </ul>
    </nav>
  )
}
