import React from "react";
import Image from "next/image";

export default function GovernmentSponsors() {
  const nigerianCompanies = [
    {
      id: 1,
      image: "/images/Ncc.png",
      name: "Nigeria Communication Commission",
    },
    {
      id: 2,
      image: "/images/NationalLattory.png",
      name: "National Lottery Regulatory Commission",
    },
    {
      id: 3,
      image: "/images/Lagos.png",
      name: "Lagos State Lottery and Gaming Authority",
    },
  ];

  return (
    <div className="relative z-20 max-w-[90%] mx-auto -mt-[12rem] rounded-[20px] shadow-lg bg-[#EEE0FA] sm:px-[255px] px-[1.5rem] py-[3rem]">
      <div className="flex items-center justify-center">
        <p className="font-semibold sm:text-[1.125rem] text-sm text-[#4E2667] whitespace-nowrap   rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
          Our Licenses
        </p>
      </div>

      <div className="flex items-center justify-center w-full">
        <p className="md:text-[1.5rem] text-[1rem] font-bold text-[#4E2667E5]/90 whitespace-nowrap">
          Whispa Konnect is Licensed By:
        </p>
      </div>

      <div className="flex  justify-center sm:gap-[3rem]  mt-[2.625rem]">
        {nigerianCompanies.map((nigeriaOrganisation) => (
          <div
            key={nigeriaOrganisation.id}
            className="flex flex-col items-center justify-center text-center"
          >
            {/* Responsive Image Wrapper */}
            <div className="relative w-[75px] h-[42px] sm:w-[183px] sm:h-[106px] ">
              <Image
                src={nigeriaOrganisation.image}
                alt={`Logo ${nigeriaOrganisation.id}`}
                fill
                className="object-contain"
              />
            </div>

            {/* Text below image */}
            <p className="mt-2 sm:text-[1.125rem] text-[0.75rem] text-[#4E2667]/90 font-semibold flex items-center justify-center">
              {nigeriaOrganisation.name}
            </p>
          </div>
        ))}
      </div>
    </div>
  );
}
