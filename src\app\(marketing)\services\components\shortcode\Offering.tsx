'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { motion, useAnimation } from 'framer-motion';

const Offering = () => {
  const Services = [
    {
      id: '1',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Dedicated Shortcodes',
      companyDescription:
        'We provide 4–6 digit shortcodes tailored to your brand’s campaign or service. Use them for two-way messaging, subscriptions, alerts, or lead capture. We handle telco registration, provisioning, and support.',
    },
    {
      id: '2',
      image: '/images/CompanyLogoOne.png',
      companyName: 'USSD Application Hosting',
      companyDescription:
        'We build and host fully interactive USSD menus that users can access by dialing a simple code like *123#. From loan requests to surveys, we design smooth, compliant flows that work on any device, even without internet.',
    },
    {
      id: '3',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Two-Way Messaging Campaigns',
      companyDescription:
        'Enable real-time interaction via SMS shortcodes or USSD — users can respond to questions, vote, confirm transactions, or claim rewards. Ideal for feedback, promo opt-ins, and customer engagement.',
    },
    {
      id: '4',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Tier-One Telco Integration',
      companyDescription:
        'We connect your campaigns to Nigeria’s top telecom providers (MTN, Airtel, Glo, 9mobile) with high throughput, low delay, and strong uptime. Enjoy reliable service even during high-volume promotions.',
    },
    {
      id: '5',
      image: '/images/CompanyLogoOne.png',
      companyName: ' Real-Time Analytics & Reporting',
      companyDescription:
        'Track engagement, delivery, and user flow with our built-in analytics dashboard. Get breakdowns by location, channel, device, and time — all exportable and audit-ready for enterprise reporting.',
    },
  ];

  const controls = useAnimation();

  useEffect(() => {
    controls.start({
      x: ['0%', '-50%'],
      transition: {
        x: {
          repeat: Infinity,
          repeatType: 'loop',
          duration: 20,
          ease: 'linear',
        },
      },
    });
  }, [controls]);

  return (
    <div className="bg-[#EEE0FA] sm:py-[4.5rem] py-[2rem] overflow-hidden">
      <div className="flex items-center justify-center">
        <p className="font-semibold sm:text-[1.125rem] text-sm text-[#4E2667] bg-[#EBCCFF] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
          What We Offer
        </p>
      </div>

      <div className="flex items-center justify-center mx-auto max-w-[54rem] mt-2 text-center px-4">
        <span className="text-[#4E2667E5]/90 font-bold sm:text-[1.875rem] text-[1rem]">
          We offer a range of USSD services to enhance your business communication and engagement.
        </span>
      </div>

      <div className="mt-[3.5rem] w-full overflow-hidden">
        <motion.div className="flex gap-[2rem] w-max" animate={controls}>
          {[...Services, ...Services].map((service, index) => (
            <div
              key={`${service.id}-${index}`}
              className="border border-[#4E266766] rounded-[1.25rem] flex-shrink-0 w-[300px]"
            >
              <div className="bg-[#E5CFF4] rounded-[1.25rem] h-full py-[2rem] px-[1.5rem] flex flex-col justify-between">
                <Image
                  src={service.image}
                  alt={service.companyName}
                  width={60}
                  height={60}
                />
                <p className="mt-[1rem] font-bold text-[1rem] text-[#4E2667] sm:text-[1.375rem]">
                  {service.companyName}
                </p>
                <p className="text-[#4E2667] leading-[1.375rem] text-sm sm:text-[1rem] mt-[0.75rem]">
                  {service.companyDescription}
                </p>
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Offering;
