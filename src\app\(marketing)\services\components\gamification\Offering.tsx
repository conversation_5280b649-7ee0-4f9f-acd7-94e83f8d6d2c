'use client';

import React, { useEffect } from 'react';
import Image from 'next/image';
import { motion, useAnimation } from 'framer-motion';

const Offering = () => {
  const Services = [
    {
      id: '1',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Spin-to-Win Mechanics',
      companyDescription:
        'Engage instantly. Reward instantly. Drive excitement and lead generation with digital spin wheels. Perfect for flash promos, coupons, and giveaways.',
    },
    {
      id: '2',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Trivia Challenges',
      companyDescription:
        'Educate, engage, and entertain. Turn knowledge into a game. Use branded quizzes to boost awareness, gather data, or enhance business decision.',
    },
    {
      id: '3',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Slot Games',
      companyDescription:
        'The thrill of the spin, now in your campaign. Create a fun and familiar experience that keeps users playing, clicking, and converting with instant slot games.',
    },
    {
      id: '4',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Crash Games',
      companyDescription:
        'Fast-paced fun. High-stakes energy. Users watch a line rise and crash, the longer they hold on, the bigger the win. Great for fintech engagement and user retention.',
    },
    {
      id: '5',
      image: '/images/CompanyLogoOne.png',
      companyName: 'Mobile Tournaments',
      companyDescription:
        'Competition that keeps them coming back. Set up leaderboards, challenges, and reward tiers. Perfect for loyalty campaigns and long-term user participation.',
    },
  ];

  const controls = useAnimation();

  useEffect(() => {
    controls.start({
      x: ['0%', '-50%'],
      transition: {
        x: {
          repeat: Infinity,
          repeatType: 'loop',
          duration: 20,
          ease: 'linear',
        },
      },
    });
  }, [controls]);

  return (
    <div className="bg-[#EEE0FA] sm:py-[4.5rem] py-[2rem] overflow-hidden">
      <div className="flex items-center justify-center">
        <p className="font-semibold sm:text-[1.125rem] text-sm text-[#4E2667] bg-[#EBCCFF] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
          Our Services
        </p>
      </div>

      <div className="flex items-center justify-center mx-auto max-w-[54rem] mt-2 text-center px-4">
        <span className="text-[#4E2667E5]/90 font-bold sm:text-[1.875rem] text-[1rem]">
          We offer a range of gamification services to enhance your business communication and engagement.
        </span>
      </div>

      <div className="mt-[3.5rem] w-full overflow-hidden">
        <motion.div className="flex gap-[2rem] w-max" animate={controls}>
          {[...Services, ...Services].map((service, index) => (
            <div
              key={`${service.id}-${index}`}
              className="border border-[#4E266766] rounded-[1.25rem] flex-shrink-0 w-[300px]"
            >
              <div className="bg-[#E5CFF4] rounded-[1.25rem] h-full py-[2rem] px-[1.5rem] flex flex-col justify-between">
                <Image
                  src={service.image}
                  alt={service.companyName}
                  width={60}
                  height={60}
                />
                <p className="mt-[1rem] font-bold text-[1rem] text-[#4E2667] sm:text-[1.375rem]">
                  {service.companyName}
                </p>
                <p className="text-[#4E2667] leading-[1.375rem] text-sm sm:text-[1rem] mt-[0.75rem]">
                  {service.companyDescription}
                </p>
              </div>
            </div>
          ))}
        </motion.div>
      </div>
    </div>
  );
};

export default Offering;
