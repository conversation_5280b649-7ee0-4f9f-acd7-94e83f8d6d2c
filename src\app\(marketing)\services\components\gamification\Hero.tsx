'use client';

import * as React from 'react';
import { Balancer } from 'react-wrap-balancer';
import Image from 'next/image';
import { Button } from '@/components/core/Button';
import { MarketingHeader } from '@/components/layout/marketing';
import Game from '../../../landingmisc/icons/game';
import Database from '../../../landingmisc/icons/Database';
import Mark from '../../../landingmisc/icons/Mark';
import Flag from '../../../landingmisc/icons/Flag';

export function Hero() {
    return (
        <section className="relative overflow-hidden">
            <MarketingHeader />

            {/* Background layers for md and above */}
            <div className="absolute inset-0 z-0 hidden md:flex">
                <div className="w-[70%] bg-[#EEE0FA]" />
                <div className="2xl:w-[47%] w-[50%] h-full bg-[url('/images/curvebackground.png')] bg-cover bg-center bg-no-repeat" />
            </div>

            {/* Main Content */}
            <div className="relative  grid grid-cols-1 md:grid-cols-2 w-full  md:gap-20">

                <div className="flex flex-col justify-end px-[1.5rem] md:px-[1.5rem] xl:px-[6.25rem]">

                    <h1 className="max-w-[90rem]">
                        <Balancer>
                            <span className='w-full text-[#4E2667E5]/90 font-bold 2xl:text-[3.25rem] xl:text-[2.5rem] lg:text-[2rem] text-[1.5rem]'>Turn Engagement Into Entertainment </span>
                        </Balancer>
                    </h1>

                    <p className=" text-[#4E2667] text-sm lg:text-[1.1875rem] md:text-[1.11rem] sm:text-[1.2rem] max-w-[42rem] mt-4 font-normal">
                        <Balancer>
                            Transform how your audience interacts with your brand. With
                            our licensed gamification engine, you can reward participation,
                            increase retention, and create unforgettable digital experiences
                        </Balancer>
                    </p>

                    <div className="[@media(max-width:380px)]:flex-col flex sm:flex-row gap-4 sm:gap-[1.5rem] mt-[2.625rem]  [@media(max-width:768px)]:mb-[2rem] sm:mb-[1rem] md:mb-[7.75rem] w-full sm:w-auto">
                        <a
                            href="https://wisewinn.com"
                            target="_blank"
                            rel="noopener noreferrer"
                        >

                            <Button className="bg-[#4E2667] sm:px-[4.5rem] px-[4.5rem] md:px-[2.8rem] lg:px-[4.5rem] py-[1rem] whitespace-nowrap text-[1rem] font-medium rounded-[0.5rem] w-full sm:w-auto shadow-md cursor-pointer">
                                Get Started
                            </Button>
                        </a>

                        {/* <Link
            
            href={'/marketingsections/contact-us'}
            >
               <Button className="text-[#4E2667] sm:px-[4.5rem] md:px-[2.8rem] lg:px-[4.5rem] py-[1rem] border border-[#4E2667] cursor-pointer whitespace-nowrap font-medium text-[1rem] rounded-[0.5rem] w-full sm:w-auto shadow-md">
              Contact Sales
            </Button>
            </Link> */}

                    </div>
                </div>

                {/* Right Image Block WITHOUT PADDING */}
                <div className="relative flex justify-center items-center">
                    {/* Mobile background */}
                    <div className="absolute inset-0 z-0 block md:hidden bg-[url('/images/curvebackground.png')] bg-cover bg-center bg-no-repeat" />

                    
                    <div className="">
                    <Image
                        alt="metaverse"
                        src="/images/MetaverseAfrican.png"
                        width={900}
                        height={753}
                        className="relative z-0 max-w-[90rem] bottom-0 "
                    />
                    </div>
                    




                    {/* <Image
                        alt="Ussd"
                        src="/images/UssdCode.png"
                        height={85}
                        width={350}
                        className=" lg:left-[10%] absolute 2xl:left-[15%] xl:left-[15%] md:left-[5%] md:top-[20%] left-[35%] top-[40%] xl:top-[30%] lg:top-[25%] -translate-x-1/2 -translate-y-1/2 z-20"
                    /> */}

                    <div className=" animate-pulse absolute 2xl:left-[20%] top-[4%] xl:left-[20%] lg:left-[20%] md:left-[20%] left-[10%] md:top-[0%] lg:top-[5%]  z-30">
                        <Game />
                    </div>
                    <div className="animate-pulse   absolute top-[10%] right-[5%] md:right-[1%] lg:right-[5%] xl:right-[25%] z-30">
                        <Database />
                    </div>
                    <div className=" animate-pulse  absolute bottom-[5%] right-[80%] z-30">
                        <Mark />
                    </div>
                    <div className="animate-pulse absolute top-[40%] right-[5%] md:right-[-1%] lg:right-[-2%] xl:right-[15%] z-30">
                        <Flag />
                    </div>

                    <Image
                        alt="marketshare"
                        src="/images/MarketShare.png"
                        height={70}
                        width={138}
                        className="absolute bottom-[3%] right-[5%] md:right-[5%] lg:right-[1%] xl:right-[20%] z-30 rounded-[0.5rem]"
                    />
                </div>
            </div>
        </section>
    );
}
