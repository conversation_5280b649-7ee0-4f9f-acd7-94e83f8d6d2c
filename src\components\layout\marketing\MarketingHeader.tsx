'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';
import Image from 'next/image';
import { cn } from '@/utils/classNames';
import { DesktopMenuBar } from './DesktopMenuBar';
import { MobileMenuDialog } from './MobileMenuModal';


const pagesWithColoredBg = ['/'];

export function MarketingHeader() {
  const pathname = usePathname();
  const isColored = pagesWithColoredBg.includes(pathname);

  return (
    <div className={cn(isColored && 'bg-[#0000]')}>
      <header
        className={cn(
          'flex w-full  items-center bg-[#EEE0FA] justify-between font-sans md:py-8 py-4  xl:px-[6.25rem] md:px-[1.5rem]  px-[1.5rem] relative ',
          isColored && 'bg-[#0000]'
        )}
      >

        <div className="flex w-full items-center justify-between relative z-10">

          <div className="flex items-center gap-4 lg:gap-10 xl:gap-[5.625rem]">
            <Link href="/">
              <span className="sr-only">Go home</span>

              <Image
                alt="Logo Image"
                className=""
                height={51}
                src="/images/WhisperLogo.png"
                width={150}
              />
            </Link>
          </div>


          <DesktopMenuBar isColored={isColored} />


          <ul className="hidden items-center gap-3 md:flex lg:gap-[1.5rem]">
            {/* <li>
              <Link href="/">
                <p className={cn(
                  'font-normal text-[1rem]',
                  isColored ? 'text-[#F9F9F9]' : 'text-[#4E2667]'
                )}>
                  Login
                </p>
              </Link>
            </li> */}
            {/* <li>
              <Link href="/">
                <Button
                  className={cn(
                    'whitespace-nowrap font-medium rounded-[0.5rem] text-sm px-[1.5rem] py-[0.625rem] cursor-pointer flex items-center gap-[1rem]',
                    isColored
                      ? 'text-[#4E2667] bg-[#F9F9F9]' // homepage style
                      : 'text-[#F9F9F9] bg-[#4E2667]' // other pages
                  )}
                  size="default"
                  variant="white"
                >
                  Get Started
                </Button>
              </Link>
            </li> */}
          </ul>


          <div className="flex items-center gap-6 md:hidden">
            <MobileMenuDialog />
          </div>
        </div>
      </header>
    </div>
  );
}
