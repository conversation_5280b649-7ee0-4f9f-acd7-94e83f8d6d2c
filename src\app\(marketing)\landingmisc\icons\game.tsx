import * as React from "react";
import { SVGProps } from "react";
const Game = (props: SVGProps<SVGSVGElement>) => (

  <svg
    width={60}
    height={60}
    viewBox="0 0 60 60"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x={12.572}
      width={48.572}
      height={48.572}
      rx={10}
      transform="rotate(15 12.572 0)"
      fill="#EAD8F9"
    />
    <g clipPath="url(#a)">
      <path
        d="M35.42 19.77a69 69 0 0 1-1.685-.805c-1.323-.663-2.523-.985-3.667-.985-2.303 0-3.897 1.329-4.897 2.328l-4.848 4.854c-2.594 2.597-3.009 5.24-1.342 8.573.258.518.524 1.086.805 1.689 1.517 3.243 3.084 6.597 5.65 6.597q.107 0 .216-.008c2.648-.207 3.399-3.616 4.062-6.623.105-.476.205-.933.307-1.343.179-.708.53-1.147 1.49-2.11l.132-.133.081-.082.214-.213c.963-.963 1.403-1.313 2.112-1.492.405-.102.861-.202 1.336-.306 3.005-.664 6.413-1.415 6.619-4.067.208-2.692-3.245-4.31-6.586-5.873m5.044 5.738c-.146 1.87-4.331 2.422-6.823 3.046-1.101.279-1.772.862-2.784 1.874l-.213.212-.212.213c-1.012 1.013-1.594 1.683-1.87 2.787-.626 2.493-1.177 6.683-3.045 6.83l-.1.004c-1.982 0-3.65-4.506-5.12-7.446-1.495-2.988-.855-4.927 1.062-6.846l1.386-1.387 2.06-2.062 1.387-1.387c1.19-1.192 2.389-1.89 3.841-1.89.887 0 1.867.259 2.997.826 2.988 1.498 7.592 3.199 7.434 5.226m-11.242.725h1.5v-1.5h-1.5zm0-2.25h1.5v-1.5h-1.5zm2.25 0h1.5v-1.5h-1.5zm0 2.25h1.5v-1.5h-1.5zm-5.74 4.504.57-.57a.72.72 0 0 0-1.018-1.02l-.57.572-.571-.571a.72.72 0 1 0-1.018 1.018l.57.571-.57.57a.72.72 0 1 0 1.018 1.019l.57-.57.588.587a.72.72 0 1 0 1.018-1.019z"
        fill="#4E2667"
      />
    </g>
    <defs>
      <clipPath id="a">
        <path fill="#fff" d="M18 18h24v24H18z" />
      </clipPath>
    </defs>
  </svg>
);
export default Game;
