"use client"

import Link from "next/link"
import { useState } from "react"
import { cn } from "@/utils/classNames"

const productItems = [
  {
    id: "lottery",
    title: "Lottery Product",
    description: "Mobile-first lottery solutions with comprehensive management tools",
    href: "/products/lottery",
    icon: "🎲",
    color: "bg-purple-100 text-purple-600",
    gradient: "from-purple-500 to-purple-700",
  },
  {
    id: "gaming",
    title: "Gaming Studio",
    description: "Powerful gamification engines that transform user engagement",
    href: "/products/gaming",
    icon: "🎮",
    color: "bg-blue-100 text-blue-600",
    gradient: "from-blue-500 to-blue-700",
  },
  {
    id: "business",
    title: "Business",
    description: "Comprehensive business solutions including CRM and analytics",
    href: "/products/business",
    icon: "💼",
    color: "bg-teal-100 text-teal-600",
    gradient: "from-teal-500 to-teal-700",
  },
  {
    id: "entertainment",
    title: "Entertainment",
    description: "Interactive entertainment solutions that engage and delight users",
    href: "/products/entertainment",
    icon: "🎭",
    color: "bg-orange-100 text-orange-600",
    gradient: "from-orange-500 to-orange-700",
  },
]

const resourcesData = {
  lottery: {
    title: "Lottery Solutions",
    description: "Transform your lottery operations with our cutting-edge mobile-first platform. From ticket sales to winner management, we've got you covered.",
    features: [
      { title: "Quick Setup", description: "Get your lottery running in minutes with our intuitive setup wizard" },
      { title: "Real-time Analytics", description: "Track sales, monitor performance, and optimize your lottery operations" },
      { title: "Secure Payments", description: "Built-in payment processing with enterprise-grade security" }
    ],
    cta: {
      primary: { text: "Start Free Trial", href: "/trial/lottery" },
      secondary: { text: "Watch Demo", href: "/demo/lottery", type: "video" }
    }
  },
  gaming: {
    title: "Gaming Studio",
    description: "Create immersive gaming experiences that keep users engaged. Our powerful engine handles everything from simple games to complex multiplayer experiences.",
    features: [
      { title: "Game Engine", description: "Build games faster with our pre-built components and templates" },
      { title: "Multiplayer Support", description: "Real-time multiplayer capabilities with global leaderboards" },
      { title: "Monetization Tools", description: "In-app purchases, ads, and subscription management built-in" }
    ],
    cta: {
      primary: { text: "Explore Games", href: "/products/gaming" },
      secondary: { text: "View Tutorials", href: "/tutorials/gaming", type: "video" }
    }
  },
  business: {
    title: "Business Solutions",
    description: "Streamline your business operations with our comprehensive suite of tools. From CRM to analytics, manage everything in one place.",
    features: [
      { title: "CRM Integration", description: "Manage customer relationships with advanced CRM capabilities" },
      { title: "Business Analytics", description: "Deep insights into your business performance and growth metrics" },
      { title: "Team Collaboration", description: "Built-in tools for team communication and project management" }
    ],
    cta: {
      primary: { text: "Get Started", href: "/products/business" },
      secondary: { text: "Case Studies", href: "/case-studies", type: "document" }
    }
  },
  entertainment: {
    title: "Entertainment Platform",
    description: "Engage your audience with interactive entertainment solutions. From live events to on-demand content, create memorable experiences.",
    features: [
      { title: "Live Streaming", description: "High-quality live streaming with interactive chat and engagement tools" },
      { title: "Content Management", description: "Organize and distribute your entertainment content effortlessly" },
      { title: "Audience Engagement", description: "Interactive polls, Q&A, and social features to boost engagement" }
    ],
    cta: {
      primary: { text: "Start Creating", href: "/products/entertainment" },
      secondary: { text: "Success Stories", href: "/success-stories", type: "document" }
    }
  }
}

interface ProductsDropdownProps {
  isOpen: boolean
  onClose: () => void
}

export function ProductsDropdown({ isOpen, onClose }: ProductsDropdownProps) {
  const [hoveredProduct, setHoveredProduct] = useState<string | null>(null)

  const currentResource = hoveredProduct ? resourcesData[hoveredProduct as keyof typeof resourcesData] : null

  return (
    <div className={cn(
      "bg-white rounded-xl shadow-xl border border-gray-100 p-6 min-w-[900px] max-w-[1000px] transition-all duration-300",
      isOpen ? "opacity-100 scale-100" : "opacity-0 scale-95 pointer-events-none"
    )}>
      <div className="grid grid-cols-2 gap-8">
        {/* Products Section */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-4 text-lg">Products</h3>
          <div className="grid grid-cols-1 gap-2">
            {productItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                onMouseEnter={() => setHoveredProduct(item.id)}
                onMouseLeave={() => setHoveredProduct(null)}
                className={cn(
                  "group flex items-start gap-4 p-4 rounded-lg transition-all duration-300 relative overflow-hidden",
                  hoveredProduct === item.id
                    ? "bg-gradient-to-r " + item.gradient + " text-white shadow-lg transform scale-105"
                    : "hover:bg-gray-50"
                )}
              >
                <div className={cn(
                  "flex items-center justify-center w-12 h-12 rounded-lg text-xl transition-all duration-300",
                  hoveredProduct === item.id
                    ? "bg-white/20 text-white"
                    : item.color
                )}>
                  {item.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className={cn(
                    "font-semibold transition-colors duration-300",
                    hoveredProduct === item.id
                      ? "text-white"
                      : "text-gray-900 group-hover:text-[#4E2667]"
                  )}>
                    {item.title}
                  </h4>
                  <p className={cn(
                    "text-sm mt-1 line-clamp-2 transition-colors duration-300",
                    hoveredProduct === item.id
                      ? "text-white/90"
                      : "text-gray-600"
                  )}>
                    {item.description}
                  </p>
                </div>
                {hoveredProduct === item.id && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
                )}
              </Link>
            ))}
          </div>
        </div>

        {/* Resources Section */}
        <div className="relative">
          <div className={cn(
            "transition-all duration-500 ease-in-out",
            currentResource ? "opacity-100 transform translate-y-0" : "opacity-60"
          )}>
            <h3 className="font-semibold text-gray-900 mb-4 text-lg">
              {currentResource ? currentResource.title : "Resources"}
            </h3>

            {currentResource ? (
              <div className="space-y-6">
                <div className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg">
                  <p className="text-gray-700 text-sm leading-relaxed mb-4">
                    {currentResource.description}
                  </p>

                  <div className="space-y-3 mb-6">
                    {currentResource.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="flex items-center justify-center w-6 h-6 rounded-full bg-[#4E2667] text-white text-xs font-semibold mt-0.5">
                          {index + 1}
                        </div>
                        <div>
                          <h5 className="font-medium text-gray-900 text-sm">{feature.title}</h5>
                          <p className="text-xs text-gray-600 mt-1">{feature.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="flex flex-col gap-3">
                    <Link
                      href={currentResource.cta.primary.href}
                      className="inline-flex items-center justify-center px-4 py-2 bg-[#4E2667] text-white text-sm font-medium rounded-lg hover:bg-[#3d1f52] transition-colors duration-200"
                    >
                      {currentResource.cta.primary.text}
                    </Link>
                    <Link
                      href={currentResource.cta.secondary.href}
                      className="inline-flex items-center justify-center px-4 py-2 border border-[#4E2667] text-[#4E2667] text-sm font-medium rounded-lg hover:bg-[#4E2667] hover:text-white transition-colors duration-200"
                    >
                      {currentResource.cta.secondary.type === 'video' ? '▶️' : '📄'} {currentResource.cta.secondary.text}
                    </Link>
                  </div>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="p-6 bg-gradient-to-br from-purple-50 to-blue-50 rounded-lg border border-purple-100">
                  <h4 className="font-semibold text-gray-900 mb-2">Explore Our Solutions</h4>
                  <p className="text-sm text-gray-600 mb-4">
                    Hover over any product to discover detailed features, resources, and get started quickly.
                  </p>
                  <div className="flex items-center gap-2 text-xs text-purple-600">
                    <span>👆</span>
                    <span>Hover on a product above to see more details</span>
                  </div>
                </div>

                <Link
                  href="/resources"
                  className="block text-sm text-[#4E2667] font-medium hover:underline"
                >
                  Browse all resources →
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
