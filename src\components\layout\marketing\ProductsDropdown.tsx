"use client"

import Link from "next/link"
import { cn } from "@/utils/classNames"

const productItems = [
  {
    title: "Lottery Product",
    description: "Mobile-first lottery solutions with comprehensive management tools",
    href: "/products/lottery",
    icon: "🎲",
    color: "bg-purple-100 text-purple-600",
  },
  {
    title: "Gaming Studio",
    description: "Powerful gamification engines that transform user engagement",
    href: "/products/gaming",
    icon: "🎮",
    color: "bg-blue-100 text-blue-600",
  },
  {
    title: "Business",
    description: "Comprehensive business solutions including CRM and analytics",
    href: "/products/business",
    icon: "💼",
    color: "bg-teal-100 text-teal-600",
  },
  {
    title: "Entertainment",
    description: "Interactive entertainment solutions that engage and delight users",
    href: "/products/entertainment",
    icon: "🎭",
    color: "bg-orange-100 text-orange-600",
  },
]

const resourceItems = [
  {
    title: "How to get started",
    description: "Jump right in — get an overview of the basics and get started on building.",
    href: "/resources/getting-started",
    number: "1",
  },
  {
    title: "Advanced features",
    description: "Once you've got the basics down, learn more about advanced features and shortcuts.",
    href: "/resources/advanced",
    number: "2",
  },
]

export function ProductsDropdown() {
  return (
    <div className="bg-white rounded-xl shadow-xl border border-gray-100 p-6 min-w-[800px] max-w-[900px]">
      <div className="grid grid-cols-2 gap-8">
        {/* Products Section */}
        <div>
          <div className="grid grid-cols-1 gap-3">
            {productItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="group flex items-start gap-4 p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className={cn("flex items-center justify-center w-10 h-10 rounded-lg text-lg", item.color)}>
                  {item.icon}
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-gray-900 group-hover:text-[#4E2667] transition-colors duration-200">
                    {item.title}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">{item.description}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>

        {/* Resources Section */}
        <div>
          <h3 className="font-semibold text-gray-900 mb-4">Resources</h3>
          <div className="space-y-3">
            {resourceItems.map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="group flex items-start gap-4 p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                <div className="flex items-center justify-center w-8 h-8 rounded-full bg-[#4E2667] text-white text-sm font-semibold">
                  {item.number}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 group-hover:text-[#4E2667] transition-colors duration-200">
                    {item.title}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">{item.description}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-xs text-[#4E2667] font-medium">Watch video</span>
                    <div className="w-1 h-1 bg-[#4E2667] rounded-full"></div>
                  </div>
                </div>
              </Link>
            ))}
            <Link href="/resources" className="block text-sm text-[#4E2667] font-medium hover:underline mt-4">
              All video tutorials →
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
