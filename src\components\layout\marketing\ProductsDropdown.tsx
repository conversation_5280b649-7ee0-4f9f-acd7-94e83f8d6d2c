'use client';

import * as React from 'react';
import Link from 'next/link';
import { cn } from '@/utils/classNames';
import LotteryIcon from '@/app/(marketing)/landingmisc/icons/LotteryIcon';
import GamingIcon from '@/app/(marketing)/landingmisc/icons/GamingIcon';
import BusinessIcon from '@/app/(marketing)/landingmisc/icons/BusinessIcon';
import EntertainmentIcon from '@/app/(marketing)/landingmisc/icons/EntertainmentIcon';
import Arrow from '@/app/(marketing)/landingmisc/icons/Arrow';

interface ProductItem {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  gradient: string;
}

const productItems: ProductItem[] = [
  {
    id: 'lottery',
    title: 'Lottery Product',
    description: 'Mobile-first lottery solutions with compliant, seamless, and rewarding mechanics for businesses.',
    icon: <LotteryIcon className="w-6 h-6" />,
    href: '/services/lottery',
    gradient: 'from-purple-500 to-pink-500'
  },
  {
    id: 'gaming',
    title: 'Gaming Studio',
    description: 'Powerful gamification engines that transform ordinary interactions into unforgettable experiences.',
    icon: <GamingIcon className="w-6 h-6" />,
    href: '/services/gamification',
    gradient: 'from-blue-500 to-purple-500'
  },
  {
    id: 'business',
    title: 'Business',
    description: 'Comprehensive business solutions including bulk messaging, USSD, and short code services.',
    icon: <BusinessIcon className="w-6 h-6" />,
    href: '/services/bulkmessaging',
    gradient: 'from-green-500 to-blue-500'
  },
  {
    id: 'entertainment',
    title: 'Entertainment',
    description: 'Interactive entertainment solutions that engage and delight your audience across all platforms.',
    icon: <EntertainmentIcon className="w-6 h-6" />,
    href: '/services/entertainment',
    gradient: 'from-orange-500 to-red-500'
  }
];

interface ProductsDropdownProps {
  className?: string;
}

export function ProductsDropdown({ className }: ProductsDropdownProps) {
  const [hoveredItem, setHoveredItem] = React.useState<string | null>(null);

  return (
    <div className={cn(
      "bg-white rounded-2xl shadow-2xl border border-gray-100 p-8 min-w-[800px] max-w-4xl",
      className
    )}>
      <div className="grid grid-cols-12 gap-8 h-full">
        {/* Left side - Product list */}
        <div className="col-span-7 space-y-2">
          {productItems.map((item) => (
            <div
              key={item.id}
              className={cn(
                "group relative p-4 rounded-xl transition-all duration-300 cursor-pointer",
                "hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50",
                "border border-transparent hover:border-purple-200",
                hoveredItem === item.id && "bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200"
              )}
              onMouseEnter={() => setHoveredItem(item.id)}
              onMouseLeave={() => setHoveredItem(null)}
            >
              <Link href={item.href} className="block">
                <div className="flex items-start gap-4">
                  <div className={cn(
                    "flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center",
                    "bg-gradient-to-br", item.gradient,
                    "text-white shadow-lg group-hover:scale-110 transition-transform duration-300"
                  )}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h3 className="font-semibold text-gray-900 text-lg mb-1 group-hover:text-purple-700 transition-colors">
                      {item.title}
                    </h3>
                    <p className="text-gray-600 text-sm leading-relaxed overflow-hidden">
                      <span className="block overflow-hidden text-ellipsis whitespace-nowrap">
                        {item.description}
                      </span>
                    </p>
                  </div>
                  <div className="flex-shrink-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <Arrow className="w-4 h-4 text-purple-600" />
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>

        {/* Right side - Featured content */}
        <div className="col-span-5 bg-gradient-to-br from-purple-100 via-pink-50 to-blue-50 rounded-xl p-6 flex flex-col justify-between">
          <div>
            <h3 className="text-xl font-bold text-gray-900 mb-4">
              Resources
            </h3>

            {/* How to get started card */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 mb-4 border border-white/50">
              <div className="flex items-start gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-bold text-sm">1</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">How to get started</h4>
                  <p className="text-gray-600 text-sm mb-3">
                    Jump right in — get an overview of the basics and get started on building.
                  </p>
                  <button className="flex items-center gap-2 text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors">
                    <div className="w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[3px] border-l-white border-y-[2px] border-y-transparent ml-0.5"></div>
                    </div>
                    Watch video
                  </button>
                </div>
              </div>
            </div>

            {/* Advanced features card */}
            <div className="bg-white/80 backdrop-blur-sm rounded-lg p-4 border border-white/50">
              <div className="flex items-start gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-bold text-sm">2</span>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Advanced features</h4>
                  <p className="text-gray-600 text-sm mb-3">
                    Once you're ready, learn more about advanced features and shortcuts.
                  </p>
                  <button className="flex items-center gap-2 text-purple-600 hover:text-purple-700 text-sm font-medium transition-colors">
                    <div className="w-4 h-4 bg-purple-600 rounded-full flex items-center justify-center">
                      <div className="w-0 h-0 border-l-[3px] border-l-white border-y-[2px] border-y-transparent ml-0.5"></div>
                    </div>
                    Watch video
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div className="mt-6">
            <Link
              href="/resources"
              className="flex items-center gap-2 text-purple-600 hover:text-purple-700 font-medium transition-colors group"
            >
              All video tutorials
              <Arrow className="w-4 h-4 group-hover:translate-x-1 transition-transform" />
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
