"use client";

import React from "react";

const Marquee = () => {
  return (
    <>
      <section className="!z-50 w-full bg-[#FFFFFF] py-[.375rem] xl:py-6 text-lg font-display">
        <div className="marquee-content">
          <div className="marquee-list font-clash flex items-center gap-28 xl:text-xl">
            {Array.from({ length: 7 }).map((_, index) => (
              <div key={index} className="flex items-center gap-3">
                <span className="w-[1.25rem] h-[1.25rem] rounded-full bg-[#4E2667]"></span>
                <p className="text-[#4E2667] font-medium font-sans">Ready to Play - Dial *20144*1*2# or Text ‘SLS’ to 20144</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      <style jsx>{`
        .marquee-content {
          overflow: hidden;
          position: relative;
          width: 100%;
          z-index: 50;
        }

        .marquee-list {
          display: flex;
          align-items: center;
          white-space: nowrap;
          animation: marquee 40s linear infinite;
        }

        .marquee-content:hover .marquee-list {
          animation-play-state: paused;
        }

        @keyframes marquee {
          0% {
            transform: translateX(100%);
          }
          100% {
            transform: translateX(-100%);
          }
        }

        @media (max-width: 640px) {
          .marquee-list {
            gap: 30px;
            font-size: 0.875rem;
          }
        }
      `}</style>
    </>
  );
};

export default Marquee;
