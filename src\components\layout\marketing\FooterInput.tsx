'use client';

import { useRouter } from 'next/navigation';
import * as React from 'react';
// import Balancer from 'react-wrap-balancer';

import { Button } from '@/components/core/Button';
import { Input } from '@/components/core/Input';


export const FooterInput: React.FunctionComponent = () => {
  const router = useRouter();

  const [email, setEmail] = React.useState('');

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();

    router.push(`/subscribe/?email=${email}`);
  };
  return (
    <div className="mt-1 flex flex-col md:basis-1/2  ">
     
      <form
        className="flex max-w-[100px] items-stretch rounded-lg transition duration-300 ease-in-out focus-within:outline-none focus-within:ring-2 focus-within:ring-[#4C1961] focus-within:ring-offset-2 md:max-w-[27.4375rem] md:flex-row md:gap-[0px] lg:gap-[0px]"
        onSubmit={handleSubmit}
      >
        <Input
          autoCapitalize="none"
          autoComplete="off"
          autoCorrect="off"
          className=" w-auto h-auto  border border-[0.3px ] border-[#4C1961]  [@media(max-width:1200px)]:w-80  xl:w-40 lg:w-40 2xl:w-80 shrink grow  rounded-none rounded-l-lg bg-[#EEE0FA] py-4   "
          id="email"
          placeholder="Your email address"
          type="text"
          value={email}
          onChange={({ target }) => {
            setEmail(target.value);
          }}
        />

        <Button
          className=" sm:w-28 h-auto w-full shrink rounded-none rounded-r-lg bg-[#4C1961] py-4 font-medium ring-0 focus-visible:ring-[#EEE0FA]"
          id="join-waitlist"
          type="submit"
        >
          <p className="font-wix-display text-sm font-medium text-[#EDE9F0] ">
            Subscribe
          </p>
        </Button>
      </form>
    </div>
  );
};
