@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);


  --font-sans: var(--font-sans);
  --font-heading: var(--font-heading);
  --font-wix-display: var(--font-wix-display);
  --font-inter: var(--font-inter);
  --font-roboto: var(--font-roboto);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, "Noto Sans", sans-serif;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-heading), var(--font-sans), ui-sans-serif, system-ui;
}




p,
span,
div,
button,
input,
textarea,
select {
  font-family: var(--font-sans), ui-sans-serif, system-ui;
}
