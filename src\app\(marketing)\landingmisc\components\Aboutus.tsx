import Link from 'next/link'
import React from 'react'
import Image from 'next/image'
import Balancer from 'react-wrap-balancer'
import ArrowRight from '../icons/Arrowright'

export default function Aboutus() {
    return (
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10 lg:gap-[4.5rem] px-4 sm:px-8 lg:px-20 py-16 bg-gradient-to-r from-[#9348B8] to-[#460666]">
          
            <div className="w-full [@media(min-width:1200px)]:flex-1">
                <p className="text-[#F9F9F9] text-sm sm:text-base font-semibold mb-2">About us</p>

                <div>
                    <Balancer >
                        <span className='font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]'>About Whispa</span>
                        <span className="block font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]/90"> Konnect Ltd.</span>
                    </Balancer>

                    <div className="mt-6  text-[#F9F9F9CC]/80">
                        <Balancer>
                            <p className="text-[1rem] sm:text-[1.25rem] ">
                                Whispa Konnect Ltd, a licensed mobile value added service company based in Lagos, Nigeria, it’s a leading, innovative, and fastest original content provider in the Nigerian telecom space, catering to the Nigerian mobile consumer successfully with its exclusively “made for mobile” content.
                            </p>
                        </Balancer>

                        <Balancer>
                            <p className="text-[1rem] sm:text-[1.25rem] mt-[1rem]">
                                Whispa Konnect is licensed by Nigerian Communication Commission as Value Added Service (Content Service using Short Code Numbers), Licensed by Lagos State Lottery and Gaming Authority to operate Winwise lottery service, and Licensed by National Lottery Regulatory Commission.
                            </p>
                        </Balancer>

                        <Link href="/marketingsections/about-us" className="flex items-center gap-3 mt-2 ">
                            <p className="text-[#F9F9F9] text-[1rem] sm:text-[1.25rem] font-normal group-hover:underline">
                                Read more
                            </p>
                            <span className="mt-1">
                                <ArrowRight />
                            </span>
                        </Link>
                    </div>
                </div>
            </div>


            {/* <div className="aspect-[586.29/587] px-[0.6787rem] py-[0.8rem] bg-[#E7EDF0] rounded-[20px] flex items-center justify-center ">
                <Image
                    alt="AboutImage"
                    src="/images/AboutWoman.png"
                    width={586.29}
                    height={587}
                    className="rounded-[20px] w-full "
                />
            </div> */}

            <div className="w-full max-w-[580px] bg-[#E7EDF0] rounded-[20px] p-3 mx-auto ">
                <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden ">
                    <Image
                        alt="AboutImage"
                        src="/images/AboutWoman.png"
                        fill
                        className="object-cover"
                    />
                </div>
            </div>



        </div>
    );
}
