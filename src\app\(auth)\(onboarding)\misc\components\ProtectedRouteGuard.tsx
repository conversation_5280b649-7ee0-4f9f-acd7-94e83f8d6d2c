'use client';

import { usePathname, useRouter } from 'next/navigation';
import * as React from 'react';

import { useAuth } from '@/contexts/authentication';
import Spinner from '@/components/core/Spinner';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

export default function ProtectedRouteGuard({ children }: ProtectedRouteProps) {
  const router = useRouter();
  const pathname = usePathname();
  const { authState } = useAuth();
  const { isAuthenticated, isLoading } = authState;

  const protectedRoutes = [
    '/dashboard',
    '/subscription',
    '/analytics/select-company',
    '/send-money',
    '/savings',
    '/buy-airtime-and-utilities',
    '/instant-wage',
    '/loans',
    '/stock',
    '/stock/business-loans/',
    '/stock/companies/',
    '/stock/branch-details',
    '/stock/teams',
    '/stock/product-and-categories/view',
    '/stock/suppliers/view-suppliers',
    '/stock/price-list',
    '/stock/stock-management',
    '/stock/add-stock/select-branch',
    '/stock/stock-out/select-company',
    '/stock/restock/select-branch',
    '/stock/stock-request/select-branch',
    '/stock/stock-tracking/select-branch',
    '/stock/stock-transfer/select-branch',
    '/stock/purchase-orders/select-branch',
    '/sales/business-loans/',
    '/sales',
    '/sales/dashboard',
    '/sales/transactions',
    '/sales/customers',
    '/sales/price-list',
    '/sales/sell',
    '/sales/manage-tables',
    '/sales/return-refund',
    '/sales/web-pos-access/select-branch',
    '/sales/invoicing-new',
    '/sales/ledger',
    '/sales/invoice-financing',
    '/sales/stock-financing',
    '/pos-shop-outlet',
    '/request-pos',
    '/employee-benefits/companies',
    '/payroll',
    '/payroll/all-companies',
    '/payroll/employee',
    '/payroll/time-and-attendance/',
    '/payroll/payroll-redirect-option/time-attendance',
    '/leave-management/companies',
    '/performance-management/company',
    '/get-linked',
    '/payroll/settings',
    '/spend-management',
    '/spend-management/overview/select-company',
    '/spend-management/companies',
    '/spend-management/teams/select-company',
    '/spend-management/requisitions/select-company',
    '/spend-management/procurement/select-company',
    '/spend-management/assets/overview/select-company',
    '/spend-management/assets/register/select-company',
    '/spend-management/suppliers/add-suppliers/select-company',
    '/spend-management/suppliers/suppliers-management/select-company',
    '/spend-management/suppliers/suppliers-settings/select-company',
    '/spend-management/budgeting/all-budgets',
    '/spend-management/expenses/all-expenses',
    '/spend-management/purchase-order/select-company',
    '/account',
    '/account/charts-of-account',
    '/account/journal-entry',
    '/account/profit-and-loss',
    '/account/balance-sheet',
    '/instant-web',
    '/instant-web/manage-website/select-company',
    '/instant-web/product',
    '/instant-web/all-products/select-branch',
    '/instant-web/add-product/select-branch',
    '/instant-web/product-categories/select-company',
    '/instant-web/qr-code/select-company',
    '/instant-web/orders',
    '/instant-web/manage-tables/select-branch',
    '/instant-web/customers/select-branch',
    '/instant-web/transactions/select-branch',
    '/instant-web/analytics/select-branch',
    '/instant-web/campaign/select-company',
    '/cards',
    '/business-loans',
    '/leaderboard',
    '/profile-settings',
    '/bank-performance',
    '/payroll/time-and-attendance/*', // wildcard dynamic path
  ];

  const _publicRoutes = [
    '/',
    '/login',
    '/sign-up',
    '/about',
    '/contact',
  ];

  const isPublicPath = React.useMemo(() => {
    return _publicRoutes.some(route => pathname === route || pathname.startsWith(route + '/'));
  }, [pathname]);

  const isProtectedPath = React.useMemo(() => {
    if (isPublicPath) return false;
    return protectedRoutes.some(route => {
      if (pathname === route) return true;
      if (route.endsWith('*')) {
        const baseRoute = route.slice(0, -1);
        return pathname.startsWith(baseRoute);
      }
      return pathname.startsWith(route + '/');
    });
  }, [pathname, isPublicPath]);

  React.useEffect(() => {
    if (!isLoading) {
      if (isAuthenticated && pathname === '/') {
        router.replace('/dashboard');
      } else if (!isAuthenticated && isProtectedPath) {
        router.replace('/login');
      }
    }
  }, [isLoading, isAuthenticated, pathname, isProtectedPath, router]);

  const LoadingUI = () => (
    <div className="flex h-screen w-screen bg-[url('/images/background_loading.jpg')] bg-no-repeat bg-cover bg-center items-center justify-center">
      <div className="flex h-screen w-screen backdrop-blur-md bg-[#080D27]/40 items-center justify-center transition-all duration-100">
        <div className="relative w-[300px] h-[300px]">
          <Spinner />
        </div>
      </div>
    </div>
  );

  if (
    isLoading ||
    (isProtectedPath && !isAuthenticated) ||
    (isAuthenticated && pathname === '/')
  ) {
    return <LoadingUI />;
  }

  return <>{children}</>;
}
