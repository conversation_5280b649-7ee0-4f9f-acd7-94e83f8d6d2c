'use client';

import React from 'react';
import Image from 'next/image'
import { Button } from '@/components/core/Button';


const bulkcardData = [
  {
    icon: '/images/Check.png', // Replace with your actual icon path
    title: 'Licensed & Regulated',
    description:
      'Licensed lottery partner under Lagos State Lottery and Gaming Authority',
  },
  {
    icon: '/images/Security.png', // Replace with your actual icon path
    title: 'End-to-End Data Security',
    description:
      'All participant data, entries, and payouts are protected with enterprise-grade encryption.',
  },
  {
    icon: '/images/Network.png', // Replace with your actual icon path
    title: 'Transparent Systems',
    description:
      'Our RNG (random number generator) algorithms meet international fairness standards.',
  },
];

export default function GamificationWorks() {
  return (
    <div className="bg-gradient-to-r from-[#9348B8] to-[#460666] pt-[4.5rem] pb-[6.0625rem] overflow-hidden relative">
      {/* Top Label */}
     <div className="flex items-center justify-center mb-4">
                <p className="font-semibold  text-sm text-[#F9F9F9] whitespace-nowrap bg-[#8528B3] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
                    Compliance & Security Assurance
                </p>
            </div>

      {/* Main Heading */}
       <div className="flex items-center justify-center mb-6">
                <p className="font-bold lg:text-[1.875rem] text-[1.2rem] text-white/90 text-center">
                    We are Licensed, Transparent, and Reliable
                </p>
            </div>

      {/* Cards Section */}
      <div className="grid md:grid-cols-3 sm:grid-cols-2 grid-cols-1 gap-6 max-w-6xl mx-auto px-4">
        {bulkcardData.map((item, index) => (
          <div
            key={index}
            className="bg-[#F3E8FF] rounded-[1.25rem] p-6 shadow-md text-left"
          >
            <div className="mb-4">
              <Image
                src={item.icon}
                alt={item.title}
                width={60}
                height={60}
                className="object-contain"
              />
            </div>
            <h3 className="text-[#24053E] font-semibold text-lg mb-2">
              {item.title}
            </h3>
            <p className="text-[#4E2667] text-sm">{item.description}</p>
          </div>
        ))}
      </div>



      <div className="flex items-center justify-center">
                     <div className="bg-[#EEE0FA] mt-[6.25rem] w-[95.6875rem] xl:mx-[6.25rem] sm:px-[4.5rem] mx-[1.5rem] px-[1.5rem] py-[1.875rem] flex flex-wrap gap-[0.75rem] justify-between rounded-[1.25rem]">
                         <p className='text-[#47235E] font-medium text-[0.75rem] sm:text-[1.5rem]'>
                             Ready to tap into the excitement of game of chance and engagement?.
                         </p>
                         <a
                             href="https://wisewinn.com"
                             target="_blank"
                             rel="noopener noreferrer"
                         >
                             <Button className='bg-[#47235E] rounded-[0.5rem] px-[2.625rem] py-[1rem] cursor-pointer'>
                                 Play Now
                             </Button>
                         </a>
                     </div>
                 </div>
    </div>
  );
}
