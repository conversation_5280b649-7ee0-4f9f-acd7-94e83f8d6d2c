import { MarketingHeader } from '@/components/layout'
import Image from 'next/image';
import React from 'react'
import ContactForm from './components/ContactForm'
import SocialMediaInfo from './components/SocialMediaInfo';

export default function Contact() {
    return (
        <div className="bg-[#EEE0FA] relative overflow-hidden">
            <MarketingHeader />
            <div className="font-sans flex items-center justify-center mx-auto max-w-[56rem] mt-[4.5rem] text-center px-4">
                <span className="text-[#4E2667E5]/90 font-bold sm:text-[3.125rem] text-[2rem]">
                    Let’s Connect, We’re Always Just a Call or a Message Away.
                </span>

            </div>

            <div className="font-sans flex items-center justify-center mx-auto max-w-[52rem] mt-[1rem] text-center px-4">
                <span className="text-[#4E2667E5]/90 font-medium sm:text-[1.125rem] text-[1rem]">
                    Whether you have a question about our services, need technical support, or want to explore a business solution, we’re here to help.
                </span>

            </div>

            <div className="w-full grid grid-cols-1  [@media(min-width:1173px)]:grid-cols-2  gap-[3.125rem] mt-[4.5rem] bg-gradient-to-r from-[#9348B8] to-[#460666] lg:px-[6.25rem] px-[1.5rem] pt-[2.625rem] pb-[6.125rem]">
                <div className="">
                    <div className="h-full relative bg-[#EEE0FA]  [@media(max-width:1173px)]:rounded-md    rounded-tl-[1rem] rounded-bl-[1rem] pt-[2.625rem] px-[2.625rem] pb-[5.6875rem]">
                        <p className='text-[#4E2667] font-sans font-semibold sm:text-[1.5rem] text-[1.2rem]  px-[#4E2667]'>Send Us a Message</p>
                        <p className='mt-4 font-sans font-medium sm:text-[1rem] text-sm'>We are a message away!</p>


                        <div className="">
                            <ContactForm />
                        </div>

                        <Image
                            alt="transparent map"
                            src="/images/transparentmap.png"
                            width={200}
                            height={200}
                            className="rounded-md absolute bottom-0 right-0 pointer-events-none  w-35 h-35"
                        />

                    </div>

                </div>

                <div className="">
                    <SocialMediaInfo/>
                </div>

            </div>


        </div>


    )
}
