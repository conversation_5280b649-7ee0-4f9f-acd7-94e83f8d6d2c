
'use client';
import React, { useState } from 'react';
import { z } from 'zod';
import { Controller, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from '@/components/core/Input';
import { Button } from '@/components/core/Button';
import { ContactFormDTO, useContactForm } from '../misc/postContactForm';
import useErrorModalState from '@/hooks/useErrorModalState';
import { ErrorModal } from '@/components/core/ErrorModal';
import { useBooleanStateControl } from '@/hooks';
import Spinner from '@/components/core/Spinner';
import ContactSuccess from '@/app/(marketing)/landingmisc/components/modal/ContactSuccess';


const whisperFormSchema = z.object({
  full_name: z.string().nonempty({ message: "This field is required" }),
  email: z.string().email({ message: "Invalid email format" }).nonempty({ message: "This field is required" }),
  subject: z.string().nonempty({ message: "This field is required" }),
  message: z.string().nonempty({ message: "This field is required" }),
  phone_number: z.string().nonempty({ message: "This field is required" }),
});


export type whisperProps = z.infer<typeof whisperFormSchema>;

export default function ContactForm() {
    const {
        control,
        handleSubmit,
        formState: { errors },
    } = useForm<whisperProps>({
        resolver: zodResolver(whisperFormSchema),
        defaultValues: {
            full_name: "",
            email: "",
            subject: "",
            message: "",
            phone_number: "",
        },
        mode: "onChange",
    });


    const { mutate: contactFormData } = useContactForm();
     const [isSubmitting, setIsSubmitting] = useState(false);

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        errorModalMessage,
    } = useErrorModalState();

 const {
        state: isSuccessOpen,
        setState: setSuccessState,
        setTrue: openSuccessModal,
        // setFalse: closeSubscriptionSummaryModal,
    } = useBooleanStateControl();


    const onSubmit = (submittedData: whisperProps) => {
        const { full_name, email, phone_number, subject, message } = submittedData;

        const payLoad: ContactFormDTO = {
            full_name,
            email,
            phone_no: phone_number,
            message: `${subject}: ${message}`

        }
   setIsSubmitting(true);
        contactFormData(payLoad, {
            onSuccess: () => {
                setIsSubmitting(false);
                openSuccessModal();
            },

            onError: (error) => {
                console.error('Error submitting the form:', error);
                
                // setLoaderModalOpen(false);
            },
        });

    }

    return (
        <div className='relative h-full'>
            <form className='mt-[1.5rem]' onSubmit={handleSubmit(onSubmit)}>
                <div>
                    {errors?.full_name && (
                        <p className="text-red-700 mt-1 text-xs font-sans">{errors?.full_name?.message}</p>
                    )}
                    <Controller
                        control={control}
                        name="full_name"
                        render={({ field }) => (
                            <Input
                                {...field}
                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.full_name ? "border-red-700" : "border-[#4E26671A]"}`}
                                placeholder="Full Name"
                            />
                        )}
                    />
                </div>

                <div className='mt-[1rem]'>
                    {errors?.email && (
                        <p className="text-red-700 mt-1 text-xs">{errors?.email?.message}</p>
                    )}
                    <Controller
                        control={control}
                        name="email"
                        render={({ field }) => (
                            <Input
                                {...field}
                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.email ? "border-red-700" : "border-[#4E26671A]"}`}
                                placeholder="Email"
                                type="email"
                            />
                        )}
                    />
                </div>

                <div className="mt-[1rem]">
                    {errors?.phone_number && (
                        <p className="text-red-700 mt-1 text-xs">{errors?.phone_number?.message}</p>
                    )}
                    <Controller
                        control={control}
                        name="phone_number"
                        render={({ field }) => (
                            <Input
                                {...field}
                                type="tel"
                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.phone_number ? 'border-red-700' : 'border-[#4E26671A]'
                                    }`}
                                placeholder="Phone no"
                                onKeyDown={(e) => {
                                    const allowedKeys = [
                                        'Backspace',
                                        'ArrowLeft',
                                        'ArrowRight',
                                        'Tab',
                                        'Delete',
                                    ];
                                    const isNumber = /^[0-9]$/.test(e.key);
                                    if (!isNumber && !allowedKeys.includes(e.key)) {
                                        e.preventDefault();
                                    }
                                }}
                            />
                        )}
                    />
                </div>


                <div className='mt-[1rem]'>
                    {errors?.subject && (
                        <p className="text-red-700 mt-1 text-xs">{errors?.subject?.message}</p>
                    )}
                    <Controller
                        control={control}
                        name="subject"
                        render={({ field }) => (
                            <Input
                                {...field}
                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.subject ? "border-red-700" : "border-[#4E26671A]"}`}
                                placeholder="Subject"
                            />
                        )}
                    />
                </div>

                <div className='mt-[1rem]'>
                    {errors?.message && (
                        <p className="text-red-700 mt-1 text-xs">{errors?.message?.message}</p>
                    )}
                    <Controller
                        control={control}
                        name="message"
                        render={({ field }) => (
                            <textarea
                                {...field}
                                rows={5}
                                className={`w-full resize-none rounded-md px-[1.5rem] py-[1.1875rem] font-medium font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.message
                                    ? "border-red-700"
                                    : "focus:border-[#4E26671A] !border-[#4E26671A]"
                                    } focus:outline-none focus:ring-0`}
                                placeholder="Message"
                            />
                        )}
                    />

                </div>

                <Button
                    className="bg-[#4E2667] text-[1rem] cursor-pointer font-medium mt-[2.625rem] font-sans [@media(max-width:640px)]:w-full flex items-center justify-center gap-2"
                    type="submit"
                    disabled={isSubmitting}
                >
                    {isSubmitting ? (
                        <>
                            <Spinner/> <p className='#4E2667'>Sending...</p>
                        </>
                    ) : (
                        "Send Message"
                    )}
                </Button>





                <ErrorModal
                    isErrorModalOpen={isErrorModalOpen}
                    setErrorModalState={setErrorModalState}
                    subheading={
                        errorModalMessage || 'Please check your inputs and try again.'
                    }
                >
                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                        <Button
                            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                            size="lg"
                            onClick={closeErrorModal}
                        >
                            Okay
                        </Button>
                    </div>
                </ErrorModal>

            </form>



            <ContactSuccess
                description="Your message has been sent successfully, our sales team will get back to you soon."
                heading="Successful"
                isSuccessOpen={isSuccessOpen}
                setSuccessState={setSuccessState}
            />

        </div>
    );
}
