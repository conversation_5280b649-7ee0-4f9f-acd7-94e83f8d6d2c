export interface AuthData {
    user: User;
    today: string;
    staffPerformance: StaffPerformance;
    downloads: Downloads;
    target: Target;
  }
  
  export interface User {
    message: string;
    user_data: UserData;
    accounts_data?: AccountsDataEntity[] | null;
    wallets_data?: WalletsDataEntity[] | null;
  }
  export interface UserData {
    id: number;
    last_login: string;
    is_active: boolean;
    date_joined: string;
    tag: string;
    first_name: string;
    last_name: string;
    username: string;
    phone_number: string;
    profile_picture: string;
    email: string;
    customer_id: string;
    unique_id: string;
    terminal_id?: null;
    terminal_serial?: null;
    terminal_provider?: null;
    date_assigned?: null;
    custom_account_provider?: null;
    type_of_user: string;
    registration_email_verified: boolean;
    referral_code?: null;
    referer_code?: null;
    state: string;
    lga: string;
    nearest_landmark: string;
    street: string;
    has_login_pin: boolean;
    merchant_pin?: null;
    has_transaction_pin: boolean;
    has_merchant_pin: boolean;
    pin_retries: number;
    pin_remaining_retries: number;
    sec_que_retries: number;
    sec_que_remaining_retries: number;
    kyc_one_image_url?: null;
    kyc_two_image_url?: null;
    kyc_level: number;
    kyc_one_progress: string;
    kyc_two_progress: string;
    kyc_three_progress: string;
    email_subscription: boolean;
    sms_subscription: boolean;
    send_money_status: boolean;
    block_on_funding: boolean;
    bypass_duplicate_trans: boolean;
    master_bvn: boolean;
    bvn_number?: null;
    bvn_first_name: string;
    bvn_last_name: string;
    first_security_question: string;
    second_security_question: string;
    sales_rep_upline_code?: null;
    sales_rep_full_name?: null;
    initial_handler?: null;
    has_sales_rep: boolean;
    business_name?: null;
    gender?: null;
    sales_rep_comm_balance_daily: number;
    sales_rep_comm_balance: number;
    bills_pay_comm_balance_daily: number;
    bills_pay_comm_balance: number;
    wallet_balance: number;
    other_comm_balance_daily: number;
    other_comm_balance: number;
    firebase_key: string;
    notify_app_token?: null;
    login_count: number;
    terminal_login_count: number;
    mobile_login_count: number;
    web_login_count: number;
    daily_terminal_login_count: number;
    weekly_terminal_login_count: number;
    monthly_terminal_login_count: number;
    terminal_last_login?: null;
    mobile_last_login: string;
    web_last_login?: null;
    is_fraud: boolean;
    lotto_suspended: boolean;
    is_suspended: boolean;
    suspension_reason?: null;
    terminal_suspended: boolean;
    mobile_suspended: boolean;
    terminal_disabled: boolean;
    mobile_disabled: boolean;
    terminal_disable_count_sum: number;
    mobile_disable_count_sum: number;
    terminal_disable_count: number;
    mobile_disable_count: number;
    role?: null;
    agent_consent: boolean;
    date_of_consent?: null;
    marital_status?: null;
    vfd_bvn_acct_num_count: number;
    terminal_status: string;
    terminal_truly_active: boolean;
    inactive_count: number;
    terminal_last_inactive?: null;
    lotto_win_toggle: boolean;
    added_trans_limit: number;
    added_daily_trans_count: number;
    trans_band: number;
    exclude_trans_band: boolean;
    change_pass_hash?: null;
    change_pass_hash_time?: null;
    ussd_active: boolean;
    ussd_active_admin_lock: boolean;
    num_of_other_accounts: number;
  }
  export interface AccountsDataEntity {
    account_type: string;
    true_account_type: string;
    bank_name: string;
    account_number: string;
    account_name: string;
    other_balance: number;
  }
  export interface WalletsDataEntity {
    wallet_type: string;
    available_balance: number;
    hold_balance: number;
  }
  
  export interface CustomerCount {
    all_customers: number;
    new_customers: number;
    active_customers: number;
    Inactive_customers: number;
    churn_customers: number;
    customer_changes: CustomerChanges;
  }
  
  export interface CustomerChanges {
    all_customers: AllCustomersChanges;
    new_customers: NewCustomersChanges;
    active_customers: ActiveCustomersChanges;
    inactive_customers: InactiveCustomersChanges;
    churn_customers: ChurnCustomersChanges;
  }
  
  export interface AllCustomersChanges {
    percentage: string | undefined;
    change: string;
  }
  
  export interface NewCustomersChanges {
    percentage: string | undefined;
    change: string;
  }
  
  export interface ActiveCustomersChanges {
    percentage: string | undefined;
    change: string;
  }
  
  export interface InactiveCustomersChanges {
    percentage: string | undefined;
    change: string;
  }
  
  export interface ChurnCustomersChanges {
    percentage: string | undefined;
    change: string;
  }
  
  export interface StaffPerformance {
    labels: string[];
    values: Record<string, number[]>;
  }
  
  export interface Downloads {
    total: number;
    completeSignups: number;
    incompleteSignups: number;
  }
  export interface Target {
    complete: number;
    required: number;
  }
  
  export type AuthState = {
    isAuthenticated: boolean;
    user: User | null | string;
    isLoading: boolean;
  };
  
  export type AuthAction =
    | { type: 'LOGIN'; payload: User }
    | { type: 'LOGOUT' }
    | { type: 'SIGNUP', payload: string }
    | { type: 'STOP_LOADING' };
  
  export type AuthDispatch = React.Dispatch<AuthAction> | null;
  
  export type LoginDto = {
    email: string;
    password: string;
  };
  
  // export type LoginPhoneNumberDto = {
  //   phone_number: string;
  //   password: string;
  // };
  
  export type LoginOtpDto = {
    phone_number: string;
    otp: string;
    referal_code: string | null;
  };
  