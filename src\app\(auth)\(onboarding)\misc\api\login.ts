"use client"

import { useAuth } from "@/contexts/authentication"

import { useMutation } from "@tanstack/react-query"



import type { LoginDto } from "../types/users"
import axios, { AxiosResponse } from "axios"
import { getUserDetails } from "./getUserDetails"
import { tokenStorage } from "../utils"

interface TokenResponse {
  access: string
  refresh: string
}

const login = (loginDto: LoginDto): Promise<AxiosResponse<TokenResponse>> =>
  axios.post("/user/login/create/", loginDto)

export const useLogin = () => {
  const { authDispatch } = useAuth()

  
  return useMutation({
    mutationFn: (loginDto: LoginDto) => login(loginDto),
    onSuccess: async (response) => {
      const { data } = response
      const { access: token } = data

      tokenStorage.setToken(token)
     
   

      const user = await getUserDetails()

      if (authDispatch) {
        authDispatch({ type: "LOGIN", payload: user })
        authDispatch({ type: "STOP_LOADING" })
      }
    },
  })
}

