'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';
import * as React from 'react';

import { cn } from '@/utils/classNames';
import useBooleanStateControl from '@/hooks/useBooleanStateControl';
import useRouteChangeEvent from '@/hooks/useRouteChangeEvent';

import DrawerMenu from '@/components/core/DrawerMenu';
import { Button } from '@/components/core/Button';
import { DrawerClose } from '@/components/core/Drawer';
import Navigationdropdownmenu from '@/app/(marketing)/landingmisc/icons/Navigationdropdownmenu';

// Your menu links
const linkGroups = [
  { text: 'Home', link: '/' },
  { text: 'Products', link: '/products' },
  { text: 'About Us', link: '/about' },
  { text: 'Services', link: '/services' },
  { text: 'Contact Us', link: '/contact' },
];

// Products submenu
const productsDropdownItems = [
  {
    text: 'Lottery Product',
    href: '/services/lottery',
  },
  {
    text: 'Gaming Studio',
    href: '/services/gamification',
  },
  {
    text: 'Business',
    href: '/services/bulkmessaging',
  },
  {
    text: 'Entertainment',
    href: '/services/entertainment',
  }
];

// Services submenu
const servicesDropdownItems = [
  {
    text: 'Bulk Messaging',
    href: '/services/bulkmessaging',
  },

  {
    text: 'Lottery',
    href: '/services/lottery'
  },

  {
    text: 'Shortcode & USSD',
    href: '/services/shortcode'
  }

];

export function MobileMenuDialog() {
  const pathname = usePathname();
  const [productsOpen, setProductsOpen] = React.useState(false);
  const [servicesOpen, setServicesOpen] = React.useState(false);
  const { setFalse: closeModal } = useBooleanStateControl();

  useRouteChangeEvent(() => closeModal());

  return (
    <DrawerMenu
      trigger={
        <Button
          className={cn(
            'md:hidden text-[#F9F9F9] bg-[#460666] px-[1.8125rem] py-[0.5rem] rounded-[10px]',
            'font-display'
          )}
        >
          Menu
        </Button>
      }
      contentClass="bg-main border-main"
    >
      <div className="text-white p-5 pb-0 gap-5">
        <header className="flex items-center justify-between">
          <h6 className="font-semibold text-lg">Menu</h6>
          <DrawerClose
            className={cn(
              'bg-white/10 h-8 w-8 rounded-full text-white/50 rotate-12 text-lg hover:text-white',
              'font-display'
            )}
          >
            X
          </DrawerClose>
        </header>

        <ul className="font-display flex flex-col gap-6 font-normal mt-10">
          {linkGroups.map((link, index) => {
            const isActive = pathname === link.link;

            if (link.text === 'Products') {
              return (
                <li key={index}>
                  <button
                    onClick={() => setProductsOpen(!productsOpen)}
                    className={cn(
                      'w-full text-left flex items-center justify-between border-b-[0.15px] border-b-white/30 p-2',
                      isActive && 'text-pink-200 font-semibold'
                    )}
                  >
                    {link.text}
                    <Navigationdropdownmenu className="w-4 h-4" />
                  </button>

                  {productsOpen && (
                    <ul className="ml-4 mt-2 space-y-2">
                      {productsDropdownItems.map((item) => (
                        <li key={item.href}>
                          <Link
                            href={item.href}
                            className="block text-sm text-white hover:text-pink-200"
                          >
                            {item.text}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              );
            }

            if (link.text === 'Services') {
              return (
                <li key={index}>
                  <button
                    onClick={() => setServicesOpen(!servicesOpen)}
                    className={cn(
                      'w-full text-left flex items-center justify-between border-b-[0.15px] border-b-white/30 p-2',
                      isActive && 'text-pink-200 font-semibold'
                    )}
                  >
                    {link.text}
                    <Navigationdropdownmenu className="w-4 h-4" />
                  </button>

                  {servicesOpen && (
                    <ul className="ml-4 mt-2 space-y-2">
                      {servicesDropdownItems.map((item) => (
                        <li key={item.href}>
                          <Link
                            href={item.href}
                            className="block text-sm text-white hover:text-pink-200"
                          >
                            {item.text}
                          </Link>
                        </li>
                      ))}
                    </ul>
                  )}
                </li>
              );
            }

            return (
              <li
                key={index}
                className={cn(
                  'border-b-[0.15px] border-b-white/30 p-2',
                  isActive && 'text-pink-200 font-semibold'
                )}
              >
                <Link href={link.link}>{link.text}</Link>
              </li>
            );
          })}
        </ul>
      </div>
    </DrawerMenu>
  );
}
