import { MarketingHeader } from '@/components/layout'
import Image from 'next/image'
import React from 'react'
import Balancer from 'react-wrap-balancer'
import Mission from './Mission'
import Vision from './Vision'
import Value from './Value'
import Sales from './Sales'
import Blank from './Blank'
import GovernmentSponsors from './GovernmentSponsors'


export default function About() {
  return (
    <div className="bg-[#EEE0FA] relative overflow-hidden">
      <MarketingHeader />
      <div className=" flex items-center justify-center mx-auto max-w-[56rem] mt-[4.5rem] text-center px-4">
        <span className="text-[#4E2667E5]/90 font-bold sm:text-[3.125rem] text-[2rem]">
          Empowering Digital Connections Across Africa.
        </span>

      </div>

      <div className="font-sans flex items-center justify-center mx-auto max-w-[52rem] mt-[1rem] text-center px-4">
        <span className="text-[#4E2667E5]/90 font-medium sm:text-[1.125rem] text-[1rem]">
          We are Whispa Konnect, a licensed innovators delivering Value Added Services, gamification, and messaging solutions to businesses of all sizes.
        </span>

      </div>

      <div className=" 2xl:px-[6.25rem] px-[1.5rem] mt-[4.5rem] flex flex-col md:flex-row items-center justify-between  gap-[4.5rem] py-[4.5rem]  bg-gradient-to-r from-[#9348B8] to-[#460666]">
      

        <div className="w-full  md:w-1/2  lg:max-w-[580px] bg-[#E7EDF0] rounded-[20px] p-3 mx-auto h-full">
          <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden h-full ">
            <Image
              alt="AboutImage"
              src="/images/AboutWoman.png"
              fill
              className="object-cover"
            />
          </div>
        </div>

       

        <div className="w-full ">
        

          <div>
            <Balancer >
            <span className='font-bold text-[2.625rem] font-sans text-[#F9F9F9E5]/90'>Who We Are</span>
            </Balancer>

            <div className="mt-6  text-[#F9F9F9CC]/80">
              <Balancer>
                <p className="sm:text-[1.5rem] text-[1.19rem] font-sans font-normal ">
                   Whispa Konnect Ltd, a licensed mobile value added service company based in Lagos, Nigeria, it’s a leading, innovative, and fastest original content provider in the Nigerian telecom space, catering to the Nigerian mobile consumer successfully with its exclusively “made for mobile” content.
                </p>
              </Balancer>

              <Balancer>
                <p className="sm:text-[1.5rem] text-[1.19rem] mt-[1rem] font-normal  ">
                  Whispa Konnect is a key player in the telecom-driven services, technology, and digital service creation, catering to industries such as Fintech, Lending, Software development, gamification, and lottery services and FMCGs.
                </p>
              </Balancer>

                <Balancer>
                <p className="sm:text-[1.5rem] text-[1.19rem] mt-[1rem] font-normal  ">
                  Whispa Konnect is licensed by Nigerian Communication Commission as Value Added Service (Content Service using Short Code Numbers), Licensed by Lagos State Lottery and Gaming Authority to operate Winwise lottery service. Licensed by National Lottery Regulatory Commission.
                </p>
              </Balancer>


            </div>
          </div>
        </div>




      </div>



      <div className="">
      <Mission/>
      </div>

    <div className="">
      <Vision/>
    </div>

    <div className="">
      <Value/>
    </div>

     <GovernmentSponsors/> 

     <div className="">
      <Blank/>
     </div>

     <div className="">
      <Sales/>
     </div>
     
    </div>
    


  )
}
