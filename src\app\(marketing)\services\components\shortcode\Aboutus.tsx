
import React from 'react'
import Image from 'next/image'
import Balancer from 'react-wrap-balancer'

import { Button } from '@/components/core/Button';


export default function Aboutus() {
    return (
        <div className="flex flex-col lg:flex-row items-center justify-between gap-10 lg:gap-[4.5rem] px-4 sm:px-8 lg:px-20 py-16 bg-gradient-to-r from-[#9348B8] to-[#460666]">

            <div className="w-full [@media(min-width:1200px)]:flex-1">
                <p className="text-[#F9F9F9] text-sm sm:text-base font-semibold mb-2">About Our</p>

                <div>
                    <Balancer>
                        <span className='font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]'>Shortcode &  </span>
                        <span className="block font-bold sm:text-[3.125rem] text-[1.5rem] text-[#F9F9F9E5]/90">USSD Services</span>
                    </Balancer>

                    <div className="mt-6  text-[#F9F9F9CC]/80">
                        <Balancer>
                            <p className="text-[1rem] sm:text-[1.25rem] ">
                                We deliver robust shortcode and USSD services that connect your brand directly to millions of mobile users—anytime, anywhere, without needing data. Our team provides end-to-end setup, telecom integration, and real-time reporting for campaigns ranging from loan applications to airtime-based gamified engagement.
                                Our shortcodes are ideal for businesses running mobile surveys, feedback collection, promo redemptions, or lotteries. We also offer tier-one telco integration with high uptime, user segmentation, and audit-level analytics for every interaction.

                            </p>
                        </Balancer>

                        <div className="[@media(max-width:640px)]:flex-col flex sm:flex-row gap-4 sm:gap-[1.5rem] mt-[2.625rem]  [@media(max-width:768px)]:mb-[2rem] sm:mb-[1rem] md:mb-[7.75rem] w-full sm:w-auto">
                            <a
                                href="https://whispersms.com"
                                target="_blank"
                                rel="noopener noreferrer"
                            >
                                <Button className="bg-[#F9F9F9] sm:px-[4.5rem] text-[#4C1961] md:px-[2.8rem] lg:px-[4.5rem] py-[1rem] whitespace-nowrap text-[1rem] font-medium rounded-[0.5rem] w-full sm:w-auto shadow-md cursor-pointer">
                                    Get Started
                                </Button>
                            </a>
                        </div>

                    </div>
                </div>
            </div>


            {/* <div className="aspect-[586.29/587] px-[0.6787rem] py-[0.8rem] bg-[#E7EDF0] rounded-[20px] flex items-center justify-center ">
                <Image
                    alt="AboutImage"
                    src="/images/AboutWoman.png"
                    width={586.29}
                    height={587}
                    className="rounded-[20px] w-full "
                />
            </div> */}

            <div className="w-full max-w-[580px] bg-[#E7EDF0] rounded-[20px] p-3 mx-auto ">
                <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden ">
                    <Image
                        alt="AboutImage"
                        src="/images/AboutBulk.png"
                        fill
                        className="object-cover"
                    />
                </div>
            </div>



        </div>
    );
}
