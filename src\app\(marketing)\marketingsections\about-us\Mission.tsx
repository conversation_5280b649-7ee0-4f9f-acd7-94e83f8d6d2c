import Image from 'next/image'

import React from 'react'
import Balancer from 'react-wrap-balancer'


export default function Mission() {
    return (

        <div className="bg-[#EEE0FA]">
 <div className="flex flex-col md:flex-row items-center justify-between gap-10 lg:gap-[4.5rem] px-4 sm:px-8 2xl:px-[6.25rem] py-16 bg-[#EEE0FA]">

            <div className="w-full ">


                <div>
                    <Balancer >
                        <span className='font-bold text-[2.625rem] font-sans text-[#4E2667E5]/90'>Our Mission</span>
                    </Balancer>

                    <div className="mt-6   ">
                        <Balancer>
                            <p className="sm:text-[1.5rem] text-[1.19rem] font-normal text-[#4E2667E5]/90">
                                To empower businesses across Africa with innovative, licensed digital tools that drive customer engagement, enhance communication, and deliver measurable results.
                            </p>
                        </Balancer>

                        <Balancer>
                            <p className="sm:text-[1.5rem] text-[1.19rem]  mt-[1rem] font-normal text-[#4E2667E5]/90 ">
                                At Whispa Konnect, we are driven by a commitment to simplify how businesses connect with their customers, whether through gamified engagement or seamless bulk messaging.
                            </p>
                        </Balancer>

                        <Balancer>
                            <p className="sm:text-[1.5rem] text-[1.19rem] mt-[1rem] font-normal  text-[#4E2667E5]/90  ">
                                Our mission is grounded in providing value-added services (VAS) that are regulatory-compliant, scalable, and user-focused. We believe that communication should be accessible, interactive, and powerful and we exist to make that possible for businesses of all sizes.
                                We do not just offer tools; we create smart digital experiences that engage, reward, and retain your audience.
                            </p>
                        </Balancer>


                    </div>
                </div>
            </div>

            <div className="md:w-1/2   rounded-[20px] mx-auto ">
                <div className="relative w-full aspect-[586.29/587] rounded-[20px] overflow-hidden ">
                    <Image
                        alt="Mission"
                        src="/images/Mission.png"
                        fill
                        className="object-cover"
                    />
                </div>
            </div>

        </div>
        </div>
       
    )
}
