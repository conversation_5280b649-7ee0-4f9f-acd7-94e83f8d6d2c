import React from 'react';
import Balancer from 'react-wrap-balancer';
import Image from 'next/image';

export default function Reason() {
  const cardData = [
    {
      id: 1,
      icon: '/images/CompanyLogoOne.png',
      text: 'Proven years of mobile marketing experience.',
      bg: '/images/darkerbackground.png', 
    },
    {
      id: 2,
      icon: '/images/CompanyLogoOne.png',
      text: 'NCC licensed wirelessapplication service provider',
      bg: '/images/lighterbackground.png', 
    },
    {
      id: 3,
      icon: '/images/CompanyLogoOne.png',
      text: 'Robust technical platform and excellent customer relationship',
      bg: '/images/lighterbackground.png', 
    },
    {
      id: 4,
      icon: '/images/CompanyLogoOne.png',
      text: 'Best practices in delivery and innovation for consumer engagement',
      bg: '/images/lighterbackground.png', 
    },
    {
      id: 5,
      icon: '/images/CompanyLogoOne.png',
      text: '24/7 technical and business support to help you win',
      bg: '/images/lighterbackground.png', 
    },
  ];

  return (
    <div className="pt-[4.5rem] pb-[11.8rem] lg:px-[6.25rem] px-[1.5rem] bg-gradient-to-r from-[#9348B8] to-[#460666]">
    
      <div className="grid grid-cols-1 [@media(min-width:1230px)]:grid-cols-2  [@media(max-width:1200px)]:gap-[2rem]">
    
        <div>
          <p className="font-semibold sm:text-[1.125rem] text-sm text-[#F9F9F9]">Why Choose Us?</p>

          <div className="mt-2">
            <Balancer>
              <span className="font-bold sm:text-[2.6875rem] text-[1.5rem] text-[#F9F9F9E5]">Why Choose</span>
              <span className="block font-bold sm:text-[2.6875rem] text-[1.5rem] text-[#F9F9F9E5]">Whispa Konnect?</span>
            </Balancer>
          </div>

          <div>
            <Balancer>
              <span className="font-normal leading-[30px] sm:text-[1.5rem] text-sm text-[#F9F9F9E5]/90">
                Here’s why hundreds of businesses trust us for gamification, engagement, messaging and entertainment.
              </span>
            </Balancer>
          </div>
        </div>

       <div className="w-full grid grid-cols-1 sm:grid-cols-2 gap-[1.1875rem]">
  {cardData.slice(0, 2).map((card) => (
    <div
      key={card.id}
      className="w-full relative min-h-[300px] rounded-[1.25rem] overflow-hidden"
    >
      {/* Background image */}
      <Image
        alt="card background"
        src={card.bg}
        fill
        className=""
      />

      {/* Overlay content */}
      <div className="absolute inset-0 flex flex-col py-[2rem] md:px-[2.625rem] px-[1.5rem]">
        <Image alt="icon" src={card.icon} width={60} height={60} />
        <p className="text-[#47235E] text-[1.25rem] md:text-[1.5rem] font-normal mt-[1rem] ">
          {card.text}
        </p>
      </div>
    </div>
  ))}
</div>

      </div>

      
      <div className="mt-[2rem] grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-[1.5rem] mb-[4.1875rem]">
        {cardData.slice(2).map((card) => (
          <div
            key={card.id}
            className="relative min-h-[300px] rounded-[1.25rem] "
          >
            <Image
              alt="card background"
              src={card.bg}
              fill
              className=""
            />
            <div className="absolute inset-0 py-[2rem]  md:px-[2.625rem] px-[1.5rem] ">
              <Image alt="icon" src={card.icon} width={60} height={60} />
              <p className="text-[#47235E] text-[1.25rem] md:text-[1.5rem] font-normal mt-[1rem] sm:mt-[2rem] max-w-md ">{card.text}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
