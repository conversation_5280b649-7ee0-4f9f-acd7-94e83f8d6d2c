'use client';

import { useRouter } from 'next/navigation';
import { Controller, useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import Image from 'next/image';

import { Dialog, DialogContent, DialogTitle } from '@/components/core/Dialog';
import { Button } from '@/components/core/Button';
import { Input } from '@/components/core/Input';

import { ClientOnly } from '@/components/core/ClientOnly';
import Delete from '../../icons/delete';

import { ContactFormDTO, useContactForm } from '@/app/(marketing)/marketingsections/contact-us/misc/postContactForm';
import { useBooleanStateControl } from '@/hooks';
import Spinner from '@/components/core/Spinner';
import { useState } from 'react';
import ContactSuccess from './ContactSuccess';
import useErrorModalState from '@/hooks/useErrorModalState';
import { ErrorModal } from '@/components/core/ErrorModal';



const whisperFormSchema = z.object({
    full_name: z.string().min(6, { message: "This field is required" }),
    email: z.string().email({ message: "Invalid email format" }).min(1, { message: "This field is required" }),
    subject: z.string().min(6, { message: "This field is required" }),
    message: z.string().min(5, { message: "This field is required" }),
    phone_number: z.string().min(10, { message: "This field is required" }),
});

type whisperProps = z.infer<typeof whisperFormSchema>;

interface ContactFormProps {
    isContactFormOpen: boolean;
    setContactFormState: React.Dispatch<React.SetStateAction<boolean>>;
    closeContactFormModal: () => void;
    heading: string;
    //   description: React.ReactNode;
}

export default function ContactForm({
    isContactFormOpen,
    setContactFormState,
    heading,
    //   description,
}: ContactFormProps) {
    const router = useRouter();

    const {
        control,
        handleSubmit,
        formState: { errors },
    } = useForm<whisperProps>({
        resolver: zodResolver(whisperFormSchema),
        defaultValues: {
            full_name: '',
            email: '',
            subject: '',
            message: '',
            phone_number: '',
        },
        mode: 'onChange',
    });

    const {
        isErrorModalOpen,
        setErrorModalState,
        closeErrorModal,
        errorModalMessage,
    } = useErrorModalState();

    const {
        state: isSuccessOpen,
        setState: setSuccessState,
        setTrue: openSuccessModal,
        // setFalse: closeSubscriptionSummaryModal,
    } = useBooleanStateControl();

    const [isSubmitting, setIsSubmitting] = useState(false);

    const { mutate: contactFormData } = useContactForm();

    const onSubmit = (submittedData: whisperProps) => {

        const { full_name, email, phone_number, subject, message } = submittedData

        const payLoad: ContactFormDTO = {
            full_name,
            email,
            phone_no: phone_number,
            message: `${subject}: ${message}`

        }

        contactFormData(payLoad, {
            onSuccess: () => {
                setIsSubmitting(false);
                openSuccessModal();
            },

            onError: (error) => {
                console.error('Error submitting the form:', error);

                // setLoaderModalOpen(false);

            },

        });


    };

    return (
        <ClientOnly>
            <Dialog open={isContactFormOpen}>
                <DialogTitle className="sr-only">Contact Form</DialogTitle>
                <DialogContent className="!overflow-hidden border border-[#c873f3]">
                    <div className="relative bg-[#EAD8F9] px-[2.625rem] pt-[2.625rem] pb-[5.75rem]">

                        <Image
                            alt="transparent map"
                            src="/images/transparentmap.png"
                            width={200}
                            height={200}
                            className="absolute bottom-0 right-0 pointer-events-none w-[8.75rem] h-[8.75rem]"
                        />


                        <div className="z-10 relative">
                            <div className="flex items-start justify-between">
                                <p className="text-[#4E2667] text-[1.5rem] font-semibold font-sans">
                                    {heading}
                                </p>
                                <button
                                    onClick={() => {
                                        setContactFormState(false);
                                        router.push('/');
                                    }}
                                    className="cursor-pointer"
                                >
                                    <Delete />
                                </button>
                            </div>



                            <form className="mt-[1.5rem]" onSubmit={handleSubmit(onSubmit)}>

                                <div>
                                    {errors?.full_name && (
                                        <p className="text-red-700 mt-1 text-xs font-sans">
                                            {errors?.full_name?.message}
                                        </p>
                                    )}
                                    <Controller
                                        control={control}
                                        name="full_name"
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667] bg-[#4E2667]/10 border ${errors?.full_name ? 'border-red-700' : 'border-[#4E26671A]'
                                                    }`}
                                                placeholder="Full Name"
                                            />
                                        )}
                                    />
                                </div>


                                <div className="mt-[1rem]">
                                    {errors?.email && (
                                        <p className="text-red-700 mt-1 text-xs">{errors?.email?.message}</p>
                                    )}
                                    <Controller
                                        control={control}
                                        name="email"
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type="email"
                                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.email ? 'border-red-700' : 'border-[#4E26671A]'
                                                    }`}
                                                placeholder="Email"
                                            />
                                        )}
                                    />
                                </div>


                                <div className="mt-[1rem]">
                                    {errors?.phone_number && (
                                        <p className="text-red-700 mt-1 text-xs">{errors?.phone_number?.message}</p>
                                    )}
                                    <Controller
                                        control={control}
                                        name="phone_number"
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type="tel"
                                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.phone_number ? 'border-red-700' : 'border-[#4E26671A]'
                                                    }`}
                                                placeholder="Phone no"
                                                onKeyDown={(e) => {
                                                    const allowedKeys = [
                                                        'Backspace',
                                                        'ArrowLeft',
                                                        'ArrowRight',
                                                        'Tab',
                                                        'Delete',
                                                    ];
                                                    const isNumber = /^[0-9]$/.test(e.key);
                                                    if (!isNumber && !allowedKeys.includes(e.key)) {
                                                        e.preventDefault();
                                                    }
                                                }}
                                            />
                                        )}
                                    />
                                </div>


                                <div className="mt-[1rem]">
                                    {errors?.subject && (
                                        <p className="text-red-700 mt-1 text-xs">{errors?.subject?.message}</p>
                                    )}
                                    <Controller
                                        control={control}
                                        name="subject"
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                className={`px-[1.5rem] py-[1.1875rem] font-normal font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.subject ? 'border-red-700' : 'border-[#4E26671A]'
                                                    }`}
                                                placeholder="Subject"
                                            />
                                        )}
                                    />
                                </div>

                                {/* Message */}
                                <div className="mt-[1rem]">
                                    {errors?.message && (
                                        <p className="text-red-700 mt-1 text-xs">{errors?.message?.message}</p>
                                    )}
                                    <Controller
                                        control={control}
                                        name="message"
                                        render={({ field }) => (
                                            <textarea
                                                {...field}
                                                rows={5}
                                                className={`w-full resize-none rounded-md px-[1.5rem] py-[1.1875rem] font-medium font-sans text-[#4E2667]  bg-[#4E2667]/10 border ${errors?.message
                                                    ? "border-red-700"
                                                    : "focus:border-[#4E26671A] !border-[#4E26671A]"
                                                    } focus:outline-none focus:ring-0`}
                                                placeholder="Message"
                                            />
                                        )}
                                    />
                                </div>

                                <Button
                                    className="bg-[#4E2667] text-[1rem] cursor-pointer font-medium mt-[2.625rem] font-sans [@media(max-width:640px)]:w-full flex items-center justify-center gap-2"
                                    type="submit"
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? (
                                        <>
                                            <Spinner /> <p className='#4E2667'>Sending...</p>
                                        </>
                                    ) : (
                                        "Send Message"
                                    )}
                                </Button>


                                <ErrorModal
                                    isErrorModalOpen={isErrorModalOpen}
                                    setErrorModalState={setErrorModalState}
                                    subheading={
                                        errorModalMessage || 'Please check your inputs and try again.'
                                    }
                                >
                                    <div className="flex gap-3 rounded-2xl bg-red-50 px-8 py-6">
                                        <Button
                                            className="grow bg-red-950 px-1.5 sm:text-sm md:px-6"
                                            size="lg"
                                            onClick={closeErrorModal}
                                        >
                                            Okay
                                        </Button>
                                    </div>
                                </ErrorModal>

                            </form>
                        </div>

                        <ContactSuccess
                            description="Your message has been sent successfully, our sales team will get back to you soon."
                            heading="Successful"
                            isSuccessOpen={isSuccessOpen}
                            setSuccessState={setSuccessState}
                        />
                    </div>
                </DialogContent>
            </Dialog>
        </ClientOnly>
    );
}
