
import { useMutation } from '@tanstack/react-query';
import axios from 'axios';


export interface ContactFormDTO {
  full_name: string;
  email: string;
  phone_no: string;
  message: string;
}


const ContactForm = async (contactFormDto: ContactFormDTO) => {
  const response = await axios.post('https://libertydraw.com/liberty/fill_contact_form/',
      contactFormDto
  );
  return response.data;
};

export const useContactForm = () => {
  return useMutation({
      mutationFn: ContactForm,
  });
};


