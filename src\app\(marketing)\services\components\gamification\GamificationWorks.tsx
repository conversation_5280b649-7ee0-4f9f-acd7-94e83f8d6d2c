import React from 'react';
import Image from 'next/image';

import { Button } from '@/components/core/Button';

const cardData = [
    {
        title: 'Motivation',
        description: 'Rewards and goals make users want to participate.',
    },
    {
        title: 'Progress',
        description: 'Levels, badges, and leaderboards keep them coming back.',
    },
    {
        title: 'Curiosity',
        description: 'Interactive challenges trigger exploration.',
    },
    {
        title: 'FOMO',
        description: 'Timed challenges or prizes encourage action now.',
    },

];



const BusinessCardData = [
    {
        title: 'Higher Engagement Rates',
        description: 'Users are 2x more likely to interact with gamified content.',
    },
    {
        title: 'Stronger Brand Recall',
        description: 'People remember experiences, not just ads.',
    },
    {
        title: 'Repeat Interactions',
        description: 'Mechanics like points, streaks, and rewards increase return visits.',
    },
    {
        title: 'Real-Time Data Capture',
        description: 'Every game interaction helps you learn more about your users in a playful, consented way.',
    },

];


export default function GamificationWorks() {
    return (
        <div className="bg-gradient-to-r from-[#9348B8] to-[#460666] pt-[4.5rem] pb-[6.0625rem] overflow-hidden relative">
            {/* Top Label */}
            <div className="flex items-center justify-center mb-4">
                <p className="font-semibold sm:text-[1.125rem] text-sm text-[#4E2667] whitespace-nowrap bg-[#EBCCFF] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">
                    Why Gamification Works
                </p>
            </div>

            {/* Main Heading */}
            <div className="flex items-center justify-center ">
                <p className="font-bold lg:text-[1.875rem] text-[1.5rem] text-white/90 text-center">
                    Why Gamification? Why Now?
                </p>
            </div>



            <div className="grid md:grid-cols-2 gap-[1.5rem] mt-[3.25rem]">
                <div className="w-[95%] md:w-[80%] bg-[#F0E1FF] rounded-2xl mx-auto relative py-[3.375rem] ">
                    {/* Titles */}
                    <div className="flex items-center justify-center px-[1.5rem]">
                        <p className=" text-[#4E2667] font-bold text-[1.1rem] sm:text-[1.25rem]">The Psychology Behind It</p>
                    </div>

                    <div className="flex items-center justify-center px-[1.5rem]">
                 <p className="text-[#4E2667]">Gamification leverages key behavioral drivers</p>
                    </div>

                    {/* Grid Cards */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6  mt-[1.75rem] px-[1.5rem] xl:px-[94px]">
                        {cardData.map((card, index) => (
                            <div
                                key={index}
                                className={`bg-white p-6 rounded-xl shadow-sm text-center border ${index < 2 ? 'border-none' : 'border-none'}`}
                            >
                                <h3 className="text-sm font-bold text-[#4E2667]">{card.title}</h3>
                                <p className="text-[0.625rem] text-[#47235E]">{card.description}</p>
                            </div>
                        ))}
                    </div>

                    {/* Decorative ring image */}
                    <div className="absolute bottom-0 right-4">
                        <Image
                            src="/images/PurpleRing.png"
                            alt="Ring"
                            width={80}
                            height={80}
                            className="opacity-30"
                        />
                    </div>
                </div>




              <div className="w-[95%] md:w-[80%] bg-[#F0E1FF] rounded-2xl mx-auto relative py-[3.375rem] ">
                    {/* Titles */}
                    <div className="flex items-center justify-center">
                        <p className=" text-[#4E2667] font-bold text-[1.25rem]">The Business Benefits</p>
                    </div>

                    <div className="flex items-center justify-center px-[1.5rem]">
                 <p className="text-[#4E2667]">Here is how brands win with gamification</p>
                    </div>

                    {/* Grid Cards */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6  mt-[1.75rem] px-[1.5rem] xl:px-[94px]">
                        {BusinessCardData.map((card, index) => (
                            <div
                                key={index}
                                className={`bg-white py-[1.5rem] px-[1.5625rem] rounded-xl shadow-sm text-center border ${index < 2 ? 'border-none' : 'border-none'}`}
                            >
                                <h3 className="text-sm font-bold text-[#4E2667] ">{card.title}</h3>
                                <p className="text-[0.625rem] text-[#47235E]">{card.description}</p>
                            </div>
                        ))}
                    </div>

                    {/* Decorative ring image */}
                    <div className="absolute bottom-0 right-4">
                        <Image
                            src="/images/PurpleRing.png"
                            alt="Ring"
                            width={80}
                            height={80}
                            className="opacity-30"
                        />
                    </div>
                </div>
            </div>
            {/* Psychology Section */}



         <div className="flex items-center justify-center">
                              <div className="bg-[#EEE0FA] mt-[6.25rem] w-[95.6875rem] xl:mx-[6.25rem] sm:px-[4.5rem] mx-[1.5rem] px-[1.5rem] py-[1.875rem] flex flex-wrap gap-[0.75rem] justify-between rounded-[1.25rem]">
                                  <p className='text-[#47235E] font-medium text-[0.75rem] sm:text-[1.5rem]'>
                                      Ready to tap into the excitement of game of chance and engagement?.
                                  </p>
                                  <a
                                      href="https://wisewinn.com"
                                      target="_blank"
                                      rel="noopener noreferrer"
                                  >
                                      <Button className='bg-[#47235E] rounded-[0.5rem] px-[2.625rem] py-[1rem] cursor-pointer'>
                                          Get Started
                                      </Button>
                                  </a>
                              </div>
                          </div>








        </div>
    );
}
