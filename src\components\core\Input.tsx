import * as React from 'react';

import { cn } from '@/utils/classNames';

export type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

export const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    const enforceMinMax = (e: React.KeyboardEvent<HTMLInputElement>) => {
      const target = e.target as HTMLInputElement;
      const value = parseInt(target.value);
      const min = parseInt(target.min);
      const max = parseInt(target.max);

      if (!isNaN(value)) {
        if (value < min) {
          target.value = min.toString();
        }
        if (value > max) {
          target.value = max.toString();
        }
      }
    };

    return (
      <input
        className={cn(
          'flex h-10 w-full rounded-md bg-input-bg px-5 py-2 text-xs transition duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-input-placeholder focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[#c873f3] focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          className
        )}
        ref={ref}
        type={type}
        onKeyUp={enforceMinMax}
        {...props}
      />
    );
  }
);
Input.displayName = 'Input';


type AmountInputProps = Omit<InputProps, "value" | "onChange"> & {
  value?: string | number
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange?: (...event: any[]) => void
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void
}

export const AmountInput = React.forwardRef<HTMLInputElement, AmountInputProps>(
  ({ value, onChange, onBlur, name, ...props }, ref) => {
    
    function formatNumber(num: number): string {
      return num.toLocaleString("en-US", { maximumFractionDigits: 0 })
    }
    
    const [displayValue, setDisplayValue] = React.useState(() => formatNumber(value ? Number(value) : 0))

    const handleChange = React.useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        const rawValue = e.target.value.replace(/[^0-9]/g, "")
        const numericValue = Number(rawValue)

        if (!isNaN(numericValue)) {
          const formattedValue = formatNumber(numericValue)
          setDisplayValue(formattedValue)
          if (onChange) {
            onChange({
              target: {
                name: name || "",
                value: numericValue,
              },
            } as unknown as React.ChangeEvent<HTMLInputElement>)
          }
        } else {
          setDisplayValue("")
          if (onChange) {
            onChange({
              target: {
                name: name || "",
                value: "",
              },
            } as unknown as React.ChangeEvent<HTMLInputElement>)
          }
        }
      },
      [onChange, name],
    )
    const handleBlur = React.useCallback(
      (e: React.FocusEvent<HTMLInputElement>) => {
        const numericValue = Number(e.target.value.replace(/[^0-9]/g, ""))
        if (!isNaN(numericValue)) {
          const formattedValue = formatNumber(numericValue)
          setDisplayValue(formattedValue)
          if (onBlur) {
            // Create a new event with the numeric value
            const syntheticEvent = {
              ...e,
              target: {
                ...e.target,
                value: numericValue.toString(),
              },
            }
            onBlur(syntheticEvent)
          }
        }
      },
      [onBlur],
    )

    React.useEffect(() => {
      if (value !== undefined) {
        setDisplayValue(formatNumber(Number(value)))
      }
    }, [value])

    return (
      <Input
        {...props}
        inputMode="numeric"
        name={name}
        ref={ref}
        type="text"
        value={displayValue}
        onBlur={handleBlur}
        onChange={handleChange}
      />
    )
  },
)

AmountInput.displayName = "AmountInput"

