'use client'

import Image from 'next/image'
import React, { useEffect } from 'react'
import { motion, useAnimation } from 'framer-motion'
import { Button } from '@/components/core/Button'
import Link from 'next/link'

const industry = [
    { id: '1', image: '/images/ecommerce.png' },
    { id: '2', image: '/images/Education.png' },
    { id: '3', image: '/images/Fintech.png' },
    { id: '4', image: '/images/Betting.png' },
    { id: '5', image: '/images/NGO.png' },
]

export default function Industry() {
    const controls = useAnimation()

    useEffect(() => {
        controls.start({
            x: ['0%', '-50%'],
            transition: {
                x: {
                    repeat: Infinity,
                    repeatType: 'loop',
                    duration: 20,
                    ease: 'linear',
                },
            },
        })
    }, [controls])

    return (
        <div className="bg-gradient-to-r from-[#9348B8] to-[#460666] pt-[4.5rem] pb-[6.0625rem] overflow-hidden ">
            <div className="flex items-center justify-center">
                <p className="text-[#F9F9F9]  font-semibold sm:text-[1.125rem] text-[0.75rem] bg-[#8528B3] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]">Industry Served</p>
            </div>

            <div className="flex items-center justify-center mx-auto max-w-[54rem] mt-2 text-center px-4">
                <span className="text-[#F9F9F9E5]/90 font-bold sm:text-[1.875rem] text-[1rem]">
                    Custom Business Solutions for Every Industry.
                </span>
            </div>

            <div className="mt-[3.9375rem] w-full overflow-hidden">
                <motion.div
                    className="flex gap-[2rem] w-max"
                    animate={controls}
                >
                    {[...industry, ...industry].map((item, index) => (
                        <div key={index} className="flex-shrink-0">
                            <Image
                                src={item.image}
                                alt={`Industry ${item.id}`}
                                width={460}
                                height={300}
                                className="rounded-lg"
                            />
                        </div>
                    ))}
                </motion.div>
            </div>



            <div className="flex items-center justify-center mt-[5.75rem] mx-[1.5rem]">


                <div className="bg-[#EEE0FA] w-[80.6875rem] sm:px-[4.5rem] px-[1.5rem] py-[1.875rem] flex flex-wrap gap-[0.75rem] justify-between rounded-[1.25rem]">
                    <p className='text-[#47235E] font-medium text-[0.75rem]  sm:text-[1.5rem]'>Ready to boost your business engagement?, our sales team is here for you.</p>
                    <Link
                    href={'/marketingsections/contact-us'}
                    
                    >
                         <Button className='bg-[#47235E] rounded-[0.5rem]'>Contact Sales</Button>
                    </Link>
                   
                </div>


            </div>

        </div>
    )
}
