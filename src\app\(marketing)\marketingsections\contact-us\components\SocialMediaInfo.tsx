'use client'
import React, { useState } from 'react'
import Copy from '@/app/(marketing)/landingmisc/icons/Copy'
import Image from 'next/image'
import Link from 'next/link'

export default function SocialMediaInfo() {
  const [copiedId, setCopiedId] = useState<number | null>(null)

  const socialContact = [
    {
      id: 1,
      image: '/images/Phone.png',
      nameText: 'Call',
      phoneNumber: '09156784983',
      icon: <Copy />,
    },
    {
      id: 2,
      image: '/images/Linkedln.png',
      nameText: 'LinkedIn',
      link: 'https://www.linkedin.com/company/whispa-konnect/',
      icon: <Copy />,
    },
    {
      id: 3,
      image: '/images/IG.png',
      nameText: 'Instagram',
      link: 'https://www.instagram.com/whispersms?igsh=YzIwYXAwMmxsaXdw',
      icon: <Copy />,
    },
    {
      id: 4,
      image: '/images/FB.png',
      nameText: 'Facebook',
      link: 'https://www.facebook.com/whisperbulksms',
      icon: <Copy />,
    },
    {
      id: 5,
      image: '/images/Email.png',
      nameText: 'Email',
      link: '<EMAIL>',
      icon: <Copy />,
    },
  ]


 const handleCopy = async (value: string, id: number) => {
  try {
    await navigator.clipboard.writeText(value)
    setCopiedId(id)
    setTimeout(() => setCopiedId(null), 1500) 
  } catch (err) {
    console.error('Copy failed:', err)
    alert('Failed to copy!')
  }
}

  return (
    <div className="h-full w-full relative pt-8 md:pt-[2.625rem] pb-12 md:pb-[6.125rem] px-4 md:px-[1.625rem] lg:px-[2.625rem] border border-[#C8BCD0] rounded-tr-xl md:rounded-tr-[1rem] rounded-br-xl md:rounded-br-[1rem] mt-8 md:mt-0">
      <p className="font-semibold text-xl md:text-[1.5rem] font-sans text-[#F9F9F9]">Contact Information</p>
      <p className="text-[#F9F9F9E5]/90 font-medium text-base md:text-[1rem] font-sans mt-4">
        Our sales team is available 24/7
      </p>
      <div className=" ">
       {socialContact.map((socials) => {
  const valueToCopy = socials.phoneNumber || socials.link || ''
  return (
    <div key={socials.id} className="flex flex-wrap items-center gap-3 mt-[1.5rem]">
      <div className="flex flex-wrap items-center [@media(max-width:400px)]:gap-1 gap-4">
        <Image src={socials.image} alt={socials.nameText} width={48} height={48} className="w-12 h-12 md:w-[60px] md:h-[60px]" />
        <span className="font-sans flex items-center text-[#F9F9F9] text-base md:text-[1.25rem] font-normal">
          <span className="[@media(max-width:768px)]:hidden">{socials.nameText}:{' '}</span>
          
          {socials.link && socials.link.startsWith('http') ? (
            <Link
              href={socials.link}
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#F9F9F9] font-semibold text-base md:text-[1.25rem] max-w-[160px] overflow-hidden whitespace-nowrap text-ellipsis block ml-2 hover:underline"
            >
              {valueToCopy}
            </Link>
          ) : (
            <span className="text-[#F9F9F9] font-semibold text-base md:text-[1.25rem] max-w-[160px] overflow-hidden whitespace-nowrap text-ellipsis block ml-2">
              {valueToCopy}
            </span>
          )}
        </span>
      </div>
      <button
        type="button"
        className="cursor-pointer flex items-center transition-opacity hover:opacity-70"
        aria-label={`Copy ${socials.nameText}`}
        onClick={() => handleCopy(valueToCopy, socials.id)}
      >
        {socials.icon}
      </button>
      {copiedId === socials.id && (
        <span className="ml-2 text-[#F9F9F9] text-xs font-semibold">Copied!</span>
      )}
    </div>
  )
})}

      </div>
     <Image
  alt="transparent map"
  src="/images/transparentmap.png"
  width={120}
  height={120}
  className="absolute bottom-2 right-2 md:bottom-0 md:right-0 pointer-events-none w-24 h-24 md:w-[200px] md:h-[200px] opacity-5"
/>
    </div>
  )
}
