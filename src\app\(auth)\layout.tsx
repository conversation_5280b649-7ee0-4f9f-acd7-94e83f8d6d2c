
import * as React from 'react';


export default function OnboardingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="h-screen-small justify-between overflow-auto bg-[#000D36] md:flex md:flex-row-reverse md:justify-normal md:pb-0">
      <div className="relative px-4 pb-4 md:flex md:basis-1/2 md:flex-col md:justify-center md:overflow-y-auto md:py-8 md:[@media(min-height:520px)]:items-center">
   
      

        <header className="relative mx-auto flex w-full max-w-[32.375rem] items-center justify-between gap-4 px-2 py-[3vh] md:hidden">
          <svg
            fill="none"
            height={43}
            viewBox="0 0 93 43"
            width={93}
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M5.232 23H.72V6.92h8.904c3.816 0 6.216 2.184 6.216 6s-2.4 6.024-6.216 6.024H5.232V23ZM9.12 10.976H5.232v3.912H9.12c1.584 0 2.232-.312 2.232-1.968 0-1.632-.648-1.944-2.232-1.944ZM20.71 23.24c-2.736 0-4.272-1.248-4.272-3.312 0-1.704 1.176-2.928 3.912-3.192l4.92-.48v-.24c0-1.224-.528-1.416-2.136-1.416-1.488 0-1.944.288-1.944 1.296v.096h-4.512v-.048c0-3.216 2.688-5.28 6.792-5.28 4.224 0 6.264 2.064 6.264 5.448V23H25.51v-2.544h-.24c-.456 1.704-1.944 2.784-4.56 2.784Zm.264-3.6c0 .384.384.456 1.08.456 2.184 0 3.072-.264 3.192-1.344l-3.696.432c-.408.048-.576.192-.576.456Zm12.762 7.44h-2.184V23h3.504c.36 0 .648-.048.84-.144l-5.784-11.952h5.136l2.088 4.752.864 2.784h.312l.792-2.832 1.8-4.704h5.04L40.408 23.48c-1.296 2.856-3.144 3.6-6.672 3.6ZM50.858 23h-4.224V6.92h4.512v7.104h.24c.36-2.016 1.704-3.36 4.56-3.36 3.552 0 5.544 2.352 5.544 6.288 0 3.912-2.04 6.288-5.736 6.288-2.832 0-4.272-1.104-4.656-3.504h-.24V23Zm.288-6.024c0 1.752.96 2.136 2.976 2.136 2.088 0 2.808-.552 2.808-2.16s-.72-2.184-2.808-2.184c-2.016 0-2.976.336-2.976 2.04v.168Zm18.239 6.264c-4.2 0-7.056-2.376-7.056-6.288 0-3.936 2.856-6.288 7.056-6.288s7.056 2.352 7.056 6.288c0 3.912-2.856 6.288-7.056 6.288Zm0-3.984c2.04 0 2.592-.552 2.592-2.304 0-1.752-.552-2.328-2.592-2.328s-2.592.576-2.592 2.328c0 1.752.552 2.304 2.592 2.304ZM82.013 23h-5.448l4.512-5.856v-.24l-4.512-6h5.52l2.472 3.648h.24l2.376-3.648h5.448l-4.512 5.928v.24L92.62 23H87.1l-2.472-3.552h-.24L82.013 23ZM1.24 40H.6v-6.7h.71v3.14h.05c.21-.85.92-1.48 2.11-1.48 1.55 0 2.4 1.07 2.4 2.57 0 1.5-.85 2.57-2.46 2.57-1.1 0-1.89-.57-2.12-1.56h-.05V40Zm.07-2.39c0 1.17.74 1.84 1.93 1.84 1.18 0 1.91-.49 1.91-1.92s-.75-1.91-1.89-1.91c-1.25 0-1.95.68-1.95 1.9v.09Zm6.083 4.09h-.62v-.65h.75c.5 0 .69-.15.88-.56l.24-.5-2.45-4.93h.78L8.423 38l.55 1.21h.06l.53-1.22 1.37-2.93h.78l-2.68 5.63c-.36.76-.82 1.01-1.64 1.01Zm11.8-1.7h-4.96v-6.7h.71v6.05h4.25V40Zm1.511-5.57h-.71V33.3h.71v1.13Zm0 5.57h-.71v-4.94h.71V40Zm1.835 0h-.64v-6.7h.71v3.14h.05c.21-.85.92-1.48 2.11-1.48 1.55 0 2.4 1.07 2.4 2.57 0 1.5-.85 2.57-2.46 2.57-1.1 0-1.89-.57-2.12-1.56h-.05V40Zm.07-2.39c0 1.17.74 1.84 1.93 1.84 1.18 0 1.91-.49 1.91-1.92s-.75-1.91-1.89-1.91c-1.25 0-1.95.68-1.95 1.9v.09Zm7.782 2.49c-1.59 0-2.6-1-2.6-2.57 0-1.5 1-2.57 2.59-2.57 1.45 0 2.48.84 2.48 2.31 0 .18-.02.33-.05.46h-4.35c.04 1.11.61 1.78 1.92 1.78 1.16 0 1.7-.43 1.7-1.15v-.07h.71v.07c0 1.03-1.02 1.74-2.4 1.74Zm-.02-4.55c-1.28 0-1.86.66-1.91 1.74h3.73v-.15c0-1.04-.66-1.59-1.82-1.59ZM34.513 40h-.71v-4.94h.64v1.35h.05c.15-.79.71-1.45 1.74-1.45 1.14 0 1.64.84 1.64 1.76v.49h-.71v-.38c0-.84-.35-1.25-1.21-1.25-1 0-1.44.63-1.44 1.74V40Zm7.213 0h-1.07c-.98 0-1.61-.41-1.61-1.57v-2.76h-.88v-.61h.88v-1.18h.72v1.18h1.96v.61h-1.96v2.8c0 .69.34.88 1.06.88h.9V40Zm1.428 1.7h-.62v-.65h.75c.5 0 .69-.15.88-.56l.24-.5-2.45-4.93h.78l1.45 2.94.55 1.21h.06l.53-1.22 1.37-2.93h.78l-2.68 5.63c-.36.76-.82 1.01-1.64 1.01ZM48.87 40h-.71v-6.7h3.08c1.45 0 2.47.83 2.47 2.26 0 1.44-1.02 2.27-2.47 2.27h-2.37V40Zm2.31-6.05h-2.31v3.23h2.31c1.18 0 1.81-.48 1.81-1.62 0-1.12-.63-1.61-1.81-1.61Zm4.668 6.15c-.97 0-1.63-.46-1.63-1.26 0-.81.67-1.16 1.59-1.26l2.35-.26v-.38c0-.96-.42-1.34-1.5-1.34-1.06 0-1.62.38-1.62 1.25v.04h-.71v-.04c0-1.04.86-1.89 2.38-1.89 1.5 0 2.14.86 2.14 1.95V40h-.64v-1.33h-.05c-.29.91-1.16 1.43-2.31 1.43Zm-.92-1.31c0 .5.33.76 1.08.76 1.2 0 2.15-.53 2.15-1.72v-.04l-2.13.24c-.74.07-1.1.25-1.1.76Zm5.737 2.91h-.62v-.65h.75c.5 0 .69-.15.88-.56l.24-.5-2.45-4.93h.78l1.45 2.94.55 1.21h.06l.53-1.22 1.37-2.93h.78l-2.68 5.63c-.36.76-.82 1.01-1.64 1.01Z"
              fill="#fff"
            />
          </svg>

        
        </header>

        {children}
      </div>

      
    </div>
  );
}
