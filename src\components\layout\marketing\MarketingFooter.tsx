"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import BackTop from "@/app/(marketing)/landingmisc/icons/BackTop"
import Facebook from "@/app/(marketing)/landingmisc/icons/Facebook"
import Instagram from "@/app/(marketing)/landingmisc/icons/Instagram"
import LinkedIn from "@/app/(marketing)/landingmisc/icons/Linkedln"
import Link from "next/link"

export function MarketingFooter() {
    const socialMediaIcons = [
        {
            id: 1,
            icon: <Facebook />,
            link: "https://www.facebook.com/whisperbulksms",
        },
        {
            id: 2,
            icon: <LinkedIn />,
            link: "https://www.linkedin.com/company/whispa-konnect/",
        },
        {
            id: 3,
            icon: <Instagram />,
            link: "https://www.instagram.com/whispersms?igsh=YzIwYXAwMmxsaXdw",
        },
    ]

    const services = [
        // { label: 'Gamification', href: '/services/gamification' },
        { label: 'Bulk Messaging', href: '/services/bulkmessaging' },
         { label: 'Lottery Services', href: '/services/lottery' },
         { label: 'Shortcode & USSD', href: '/services/shortcode' },
    ];

    const [isVisible, setIsVisible] = useState(false)

    useEffect(() => {
        const toggleVisibility = () => {
            if (window.scrollY > 500) {
                setIsVisible(true)
            } else {
                setIsVisible(false)
            }
        }

        window.addEventListener("scroll", toggleVisibility)
        return () => {
            window.removeEventListener("scroll", toggleVisibility)
        }
    }, [])

    const scrollToTop = () => {
        window.scrollTo({
            top: 0,
            behavior: "smooth",
        })
    }

    return (
        <footer className="w-full bg-[#EEE0FA] lg:px-[6.25rem] px-[1.5rem] py-[2.625rem]">
            <div className="flex justify-between gap-8  flex-wrap lg:flex-nowrap">
                {/* Logo and Description */}
                <div className="flex-1 max-w-[300px]">
                    <Image alt="footerLogo" src="/images/FooterLogo.png" width={100} height={24} />
                    <div className="mt-[1rem]">
                        <p className="text-[#47235E] text-sm font-normal leading-relaxed">
                            Whispa Konnect Ltd is licensed by Nigerian Communication Commission as Value Added Service, licensed by
                            Lagos State Lottery and Gaming Authority, Licensed by National Lottery Regulatory Commission.
                        </p>
                    </div>
                </div>

                {/* Quick Links */}
                <div className="flex-shrink-0">
                    <h3 className="text-[#47235E] font-medium text-[1.125rem] mb-4">Quick Links</h3>
                    <div className="space-y-3">
                      
                        <Link
                            href="/marketingsections/about-us"
                            className="block text-[#47235E] font-normal text-sm hover:opacity-80 transition"
                        >
                            About us
                        </Link>
                        <Link
                            href="/marketingsections/contact-us"
                            className="block text-[#47235E] font-normal text-sm hover:opacity-80 transition"
                        >
                            Contact us
                        </Link>
                    </div>
                </div>

                {/* Services */}
                <div className="flex-shrink-0">
                    <h3 className="text-[#47235E] font-medium text-[1.125rem] mb-4">Services</h3>
                    <div className="space-y-3">
                        {services.map((service) => (
                            <Link key={service.label} href={service.href}>
                                <p className="text-[#47235E] font-normal text-sm mt-[1rem] cursor-pointer ">
                                    {service.label}
                                </p>
                            </Link>
                        ))}
                    </div>
                </div>

                {/* Location */}
                <div className="flex-shrink-0">
                    <h3 className="text-[#47235E] font-medium text-[1.125rem] mb-4">Location</h3>
                    <p className="max-w-[200px] text-sm font-normal text-[#47235E] leading-relaxed">
                        No 27, Alara Street, off Commercial Avenue, Sabo Yaba, Lagos
                    </p>
                </div>


            </div>

            {/* Social Media Section */}
            <div className="mt-8 flex justify-between items-start">
                {/* Left Side: Follow Us + Social Icons */}
                <div>
                    <p className="text-[#47235E] font-medium text-sm mb-2">Follow Us</p>
                    <div className="flex gap-4">
                        {socialMediaIcons.map((social) => (
                            <a
                                key={social.id}
                                href={social.link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="hover:opacity-80 transition"
                            >
                                {social.icon}
                            </a>
                        ))}
                    </div>
                </div>

                {/* Right Side: Back to Top */}
                {isVisible && (
                    <div className="cursor-pointer" onClick={scrollToTop}>
                        <BackTop />
                    </div>
                )}
            </div>

            {/* Divider */}
            <div className="mt-8 h-[1px] w-full bg-[#47235E]"></div>

            {/* Bottom Links */}
            <div className="flex flex-wrap items-center justify-between w-full mt-4 gap-4">
                <p className="text-[#47235E] text-sm font-normal">All rights reserved. &copy; Whispa Konnect</p>
                <Link href="#" className="text-[#47235E] text-sm font-normal hover:opacity-80 transition">
                    Privacy Policy
                </Link>
                <Link href="#" className="text-[#47235E] text-sm font-normal hover:opacity-80 transition">
                    Terms & Conditions
                </Link>
            </div>
        </footer>
    )
}
