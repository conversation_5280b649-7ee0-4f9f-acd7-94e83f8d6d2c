import * as React from "react";
import { SVGProps } from "react";

const GamingIcon = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={24}
    height={24}
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M6 12a6 6 0 1 1 12 0 6 6 0 0 1-12 0Z"
      stroke="currentColor"
      strokeWidth={2}
      fill="none"
    />
    <path
      d="M8 10h8M8 14h8"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
    />
    <circle cx={10} cy={8} r={1} fill="currentColor" />
    <circle cx={14} cy={8} r={1} fill="currentColor" />
    <path
      d="M2 12h4M18 12h4"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
    />
    <path
      d="M12 2v4M12 18v4"
      stroke="currentColor"
      strokeWidth={2}
      strokeLinecap="round"
    />
  </svg>
);

export default GamingIcon;
