
"use client"
import React, { ReactNode } from 'react'
import { ClientOnly } from '@/components/core/ClientOnly';
import { Dialog, DialogContent, DialogTitle } from '@/components/core/Dialog';
import { Button } from '@/components/core/Button';

// import { DialogBody } from '@/components/core/Dialog2';
import { useRouter } from 'next/navigation';
import Success from '@/app/(marketing)/marketingsections/contact-us/icons/Success';
import { DialogBody } from '@/components/core/Dialog2';






interface UseBooleanStateControlProps {
    isSuccessOpen: boolean;
    setSuccessState: React.Dispatch<
        React.SetStateAction<boolean>
    >;

    heading: string;
    description: ReactNode;
    children?: React.ReactNode;
}


function ContactSuccess({

    isSuccessOpen,
    setSuccessState,

    heading,
    description,

    // children,


}: UseBooleanStateControlProps) {


    const router = useRouter();

    const goHome = () => {
        router.push('/')
    }

    return (

        <div className="rounded-xl">



          <ClientOnly>

                <Dialog open={isSuccessOpen}
                    onOpenChange={setSuccessState}>

                    <DialogContent className="!overflow-hidden border border-[#D5C5E3] w-full">

                <DialogTitle className="sr-only">Contact Success Modal</DialogTitle>



                        <DialogBody className="bg-[#D5C5E3]  w-full   border-[0.01px] border-[#D5C5E3] py-[2.9375rem] ">

                            <div className="">

                                <div className=''>

                                    <div className="flex items-center justify-center w-full mt-[2.125rem]">
                                       <Success/>
                                    </div>

                                    <div className="flex justify-center items-center w-full mt-[1.5rem]">

                                        <p className=" font-bold text-[1.25rem] leading-[2.125rem] text-[#4E2667]">{heading}</p>

                                    </div>

                                    <div className="text-center px-[3rem] mt-[0.5rem] ">

                                        <p className="text-[0.875rem] text-[#4E2667CC] font-normal  ">{description}</p>

                                    </div>

                                


                                    <div className=' mt-[2rem] rounded-[1rem] py-[1.5rem]'>

                                        <div className='flex items-center justify-center'>

                                           <Button className=' cursor-pointer px-[9.375rem] py-[0.8125rem] bg-[#4E2667]'
                                          onClick={goHome}
                                            >Done</Button>

                                        </div>



                                    </div>


                                </div>



                            </div>

                        </DialogBody>




                    </DialogContent>


                </Dialog>
            </ClientOnly>


        </div>


    )


}

export default ContactSuccess;









