import * as React from "react";
import { SVGProps } from "react";
const Success = (props: SVGProps<SVGSVGElement>) => (
  <svg
    width={60}
    height={60}
    viewBox="0 0 60 60"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx={30} cy={30} r={30} fill="#D2BFE2" />
    <circle cx={30} cy={30} r={25} fill="#DEC5F4" />
    <path
      d="M29.5 30.5 33 27m6.048-5.947c-2.178-2.346-18.562 3.4-18.548 5.497.015 2.379 6.398 3.11 8.167 3.607 1.064.299 1.349.604 1.594 1.72 1.111 5.052 1.67 7.566 2.94 7.622 2.027.09 7.972-16.157 5.847-18.446"
      stroke="#15A521"
      strokeWidth={1.5}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export default Success;
