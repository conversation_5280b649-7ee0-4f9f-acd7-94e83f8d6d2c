import React from 'react'

import Image from 'next/image'

export default function Testimonial() {

     const Testimonials = [
    {
      id: '1',
      image: '/images/TestimonialImage.png',
      personName: '<PERSON><PERSON><PERSON><PERSON>',
      testimonialContent:
        '“With WhisperSMS, we reach thousands of customers in seconds. Reliable delivery, and easy to use.”',

        professionName: 'Human Resource Manager, Levelers Nigeria Plc.'
      
    },
    {
      id: '2',
      image: '/images/TestimonialImage.png',
      personName: 'Chinedu Okafor',
      testimonialContent:
        '“Whispa Konnect’s gamification feature helped us double our user retention within 3 months. Winwise is a game changer.”',
         professionName: 'Growth Lead, XYZ Digitals'
     
    },
    {
      id: '3',
      image: '/images/TestimonialImage.png',
      personName: 'Aderinsola Adebayo',
      testimonialContent:
        '“What stood out for us was their customer support. The team always goes above and beyond.”',
         professionName: 'CEO, iGames Africa'
     
    },
    {
      id: '4',
      image: '/images/TestimonialImage.png',
      personName: '<PERSON><PERSON><PERSON><PERSON>',
      testimonialContent:
        '“With WhisperSMS, we reach thousands of customers in seconds. Reliable delivery, and easy to use.”',
          professionName: 'Human Resource Manager, Levelers Nigeria Plc.'
    },
    {
      id: '5',
      image: '/images/TestimonialImage.png',
      personName: 'Chinedu Okafor',
      testimonialContent:
        '“Whispa Konnect’s gamification feature helped us double our user retention within 3 months. Winwise is a game changer”',
         professionName: ' Growth Lead, XYZ Digitals.'

    
    },

      {
      id: '6',
      image: '/images/TestimonialImage.png',
      personName: 'Aderinsola Adebayo',
      testimonialContent:
        '““What stood out for us was their customer support. The team always goes above and beyond.””',
          professionName: 'CEO, iGames Africa'
    
    },
  ];
  return (
    <div className="bg-[#EEE0FA] py-[4.5rem]">
  <div className=" flex items-center justify-center w-full">
<p className='text-[#4E2667] font-semibold sm:text-[1.125rem] text-sm bg-[#EBCCFF] rounded-[2.5rem] font-sans px-[1.5rem] py-[0.5rem]'>Our Clients Say It Best</p>

  </div>

   <div className="flex items-center justify-center mx-auto max-w-[54rem] mt-2 text-center px-4">
        <span className="text-[#4E2667E5]/90 font-bold sm:text-[1.875rem] text-[1rem] ">
        Real stories from our clients
        </span>
      </div>




<div className="mt-[4.5rem]  grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-[1.5rem] lg:px-[6.25rem] px-[1.5rem]">
        {Testimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className="border border-[#4E266766] rounded-[1.25rem]" 
          >    
            <div className="  bg-[#E5CFF4] rounded-[1.25rem] h-full py-[2.625rem] px-[2rem] ">
              <Image
                src={testimonial.image}
                alt={testimonial.personName}
                width={60}
                height={60}
                className=""
              />
              <p className="mt-[1.5rem] font-bold sm:text-[1.25rem] text-[1rem] text-[#4E2667] ">{testimonial.personName}</p> 
            <p className="text-[#4E2667] leading-[1.375rem] text-sm sm:text-[1rem] mt-[1rem] ">{testimonial.testimonialContent}</p>
             <p className="text-[#4E2667] sm:text-[1rem] text-sm font-semibold mt-[1rem] ">{testimonial.professionName}</p>

             
            </div>
          </div>
        ))}
      </div>




    </div>


   
  )
}
