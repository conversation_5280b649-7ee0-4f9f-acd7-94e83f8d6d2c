import React from 'react'
import Image from 'next/image';
import Balancer from 'react-wrap-balancer';
export default function Value() {

    const cardData = [
    {
      id: 1,
      icon: '/images/CompanyLogoOne.png',
      text: 'Integrity',
      subText: 'We’re licensed and accountable.',
      bg: '/images/darkerbackground.png', 
    },
    {
      id: 2,
      icon: '/images/CompanyLogoOne.png',
      text: 'Innovation',
      subText: 'We build for the future.',
      bg: '/images/lighterbackground.png', 
    },
    {
      id: 3,
      icon: '/images/CompanyLogoOne.png',
      text: 'Customer Success',
      subText: 'Your growth is our priority.',
      bg: '/images/lighterbackground.png', 
    },
    {
      id: 4,
      icon: '/images/CompanyLogoOne.png',
      text: 'Compliance First',
      subText: 'We operate with full regulatory backing.',
      bg: '/images/lighterbackground.png', 

    },
    {
      id: 5,
      icon: '/images/CompanyLogoOne.png',
      text: 'Excellence',
    subText: 'We deliver nothing less than our best.',
      bg: '/images/lighterbackground.png', 
    },
  ];
  return (
   <div className="pt-[4.5rem] pb-[11.8rem] lg:px-[6.25rem] px-[1.5rem] bg-gradient-to-r from-[#9348B8] to-[#460666]">
    
      <div className="grid grid-cols-1 [@media(min-width:1230px)]:grid-cols-2  gap-[2rem]">
    
        <div className=''>
          <div className="mt-2">
            <Balancer>
              <span className="font-bold sm:text-[2.6875rem] text-[1.5rem] text-[#F9F9F9E5]/90">The Values That</span>
              <span className="block font-bold sm:text-[2.6875rem] text-[1.5rem] text-[#F9F9F9E5]/90">Drive Us.</span>
            </Balancer>
          </div>

          <div>
            <Balancer>
              <span className="font-normal leading-[30px] sm:text-[1.5rem] text-sm text-[#F9F9F9E5]/90">
                At Whispa Konnect, our values shape everything we do from how we build to how we serve.
              </span>
            </Balancer>
          </div>
        </div>

       <div className="grid grid-cols-1 sm:grid-cols-2 gap-[1.1875rem]">
  {cardData.slice(0, 2).map((card) => (
    <div
      key={card.id}
      className="relative min-h-[300px] rounded-[1.25rem] overflow-hidden"
    >
      {/* Background image */}
      <Image
        alt="card background"
        src={card.bg}
        fill
        className=""
      />

      {/* Overlay content */}
      <div className="absolute inset-0 flex flex-col py-[2rem] md:px-[2.625rem] px-[1.5rem]">
        <Image alt="icon" src={card.icon} width={60} height={60} />
        <p className="text-[#47235E] font-bold text-[1.25rem] md:text-[1.5rem]  mt-[1rem] sm:mt-[1.5rem]">
          {card.text}
        </p>
          <p className="text-[#47235E] text-[1.25rem]  font-normal mt-[0.5rem]  max-w-md ">
          {card.subText}
        </p>
      </div>
    </div>
  ))}
</div>

      </div>

      
      <div className="mt-[2rem] grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-[1.5rem] mb-[4.1875rem]">
        {cardData.slice(2).map((card) => (
          <div
            key={card.id}
            className="relative min-h-[300px] rounded-[1.25rem] "
          >
            <Image
              alt="card background"
              src={card.bg}
              fill
              className=""
            />
            <div className="absolute inset-0 py-[2rem]  md:px-[2.625rem] px-[1.5rem] ">
              <Image alt="icon" src={card.icon} width={60} height={60} />
              <p className="text-[#47235E] text-[1.25rem] md:text-[1.5rem]  mt-[1rem] sm:mt-[2rem] max-w-md font-bold">{card.text}</p>
              <p className="text-[#47235E] text-[1.25rem]  font-normal mt-[0.5rem]  max-w-md ">{card.subText}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
