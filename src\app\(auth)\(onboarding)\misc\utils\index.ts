const TOKEN_STORAGE_PREFIX = 'WhisperConnect';

export const tokenStorage = {
  getToken: () => {
    if (typeof window === 'undefined') return null;
    const token = window.localStorage.getItem(`${TOKEN_STORAGE_PREFIX}TOKEN`);
    return token ? JSON.parse(token) : null;
  },

  setToken: (token: string) => {
    if (typeof window === 'undefined') return;
    window.localStorage.setItem(
      `${TOKEN_STORAGE_PREFIX}TOKEN`,
      JSON.stringify(token),
    );
  },

  clearToken: () => {
    if (typeof window === 'undefined') return;
    window.localStorage.removeItem(`${TOKEN_STORAGE_PREFIX}TOKEN`);
  },
};
